# -*- coding: utf-8 -*-
"""
会员等级消费分析图片生成模块
生成会员等级的人均消费额、客单价、平均消费频次的混合图表
"""

import datetime
import logging
from typing import Dict, Any, List, Optional
from pathlib import Path

# 设置matplotlib后端（在导入pyplot之前）
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

logger = logging.getLogger(__name__)

class LevelConsumptionPicGenerator:
    """会员等级消费分析图片生成器"""

    def __init__(self, bid: str, image_manager):
        """
        初始化图片生成器

        Args:
            bid: 品牌ID
            image_manager: 图片管理器实例
        """
        self.bid = bid
        self.image_manager = image_manager

    def _extract_param(self, query_params, key, default=None):
        """
        从查询参数中提取值

        Args:
            query_params: 查询参数（对象或字典）
            key: 参数键
            default: 默认值

        Returns:
            参数值
        """
        if hasattr(query_params, key):
            return getattr(query_params, key)
        elif isinstance(query_params, dict):
            return query_params.get(key, default)
        else:
            return default

    async def generate_level_consumption_chart(self, query_params) -> Dict[str, str]:
        """
        生成会员等级消费分析图表

        Args:
            query_params: 查询参数（对象或字典）

        Returns:
            Dict: 包含图片路径的字典
        """
        try:
            logger.info(f"开始生成会员等级消费分析图表 - bid: {self.bid}")
            logger.info(f"查询参数类型: {type(query_params)}")
            logger.info(f"图片管理器会话目录: {self.image_manager.session_dir}")

            # 提取查询参数
            start_date = self._extract_param(query_params, 'start_date', '2025-06-01')
            end_date = self._extract_param(query_params, 'end_date', '2025-06-30')
            bid = self._extract_param(query_params, 'bid', self.bid)
            sid = self._extract_param(query_params, 'sid', None)

            logger.info(f"查询时间范围: {start_date} 到 {end_date}")
            logger.info(f"品牌ID: {bid}, 门店ID: {sid}")

            # 获取会员等级消费数据
            level_data = await self._fetch_level_consumption_data(start_date, end_date, bid, sid)

            # 初始化结果字典
            result = {}

            # 如果数据获取失败，生成错误图片
            if not level_data:
                logger.warning("会员等级消费数据获取失败，生成错误图片")
                error_path = self._generate_error_image("level_consumption", "会员等级消费数据获取失败")
                if error_path:
                    result["level_consumption"] = error_path
            else:
                # 生成正常图表
                chart_path = await self._generate_chart(
                    level_data,
                    "会员等级消费分析",
                    "level_consumption"
                )
                if chart_path:
                    result["level_consumption"] = chart_path

            # 生成AI分析报告（无论数据是否完整都尝试生成分析）
            try:
                from .PictureAi import PictureAiAnalyzer
                ai_analyzer = PictureAiAnalyzer()

                if level_data:
                    # 有数据时生成正常分析
                    ai_analysis = await ai_analyzer.generate_level_consumption_analysis(level_data, start_date, end_date)
                    result.update(ai_analysis)
                else:
                    # 无数据时生成默认分析
                    result["level_consumption_analysis_report"] = "1、会员等级消费数据缺失，无法进行详细分析。\n2、建议检查数据收集机制，确保等级消费数据完整性。\n3、可通过其他渠道补充等级消费数据。\n4、建立完善的等级消费数据监控体系。"

                logger.info("会员等级消费AI分析生成完成")
            except Exception as ai_error:
                logger.error(f"生成会员等级消费AI分析失败: {ai_error}")
                result["level_consumption_analysis_report"] = "1、AI分析系统暂时不可用，请稍后重试。\n2、建议检查AI服务连接状态和配置。\n3、可暂时使用人工分析替代AI分析功能。\n4、联系技术支持解决AI分析问题。"

            logger.info(f"会员等级消费分析图表生成完成，共生成 {len(result)} 个结果")
            return result

        except Exception as e:
            logger.error(f"生成会员等级消费分析图表失败: {e}")
            import traceback
            traceback.print_exc()
            return {}

    def _generate_error_image(self, image_type: str, error_message: str) -> str:
        """
        生成错误提示图片

        Args:
            image_type: 图片类型
            error_message: 错误信息

        Returns:
            str: 图片保存路径
        """
        try:
            # 创建错误图片
            fig, ax = plt.subplots(figsize=(12, 8))
            ax.text(0.5, 0.5, f'数据生成失败\n{error_message}',
                   horizontalalignment='center', verticalalignment='center',
                   fontsize=20, color='red', weight='bold',
                   transform=ax.transAxes)
            ax.set_xlim(0, 1)
            ax.set_ylim(0, 1)
            ax.axis('off')

            # 保存图片
            file_path = self.image_manager.get_image_path(image_type)
            plt.savefig(file_path, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close(fig)

            logger.info(f"错误图片生成完成: {file_path}")
            return file_path

        except Exception as e:
            logger.error(f"生成错误图片失败: {e}")
            return ""

    async def _fetch_level_consumption_data(self, start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        获取会员等级消费数据

        Args:
            start_date: 开始日期 (YYYY-MM-DD格式)
            end_date: 结束日期 (YYYY-MM-DD格式)
            bid: 品牌ID
            sid: 门店ID (可选)

        Returns:
            List: 会员等级消费数据列表
        """
        try:
            from api.query.MemberLevels.MemberLevelsSql import MemberLevelsSqlQueries
            from core.database import db

            logger.info(f"开始获取会员等级消费数据: {start_date} 到 {end_date}")

            # 1. 获取人均消费额数据
            per_capita_sql = MemberLevelsSqlQueries.get_per_capita_consumption_sql(start_date, end_date, bid, sid)
            per_capita_results = await db.execute_welife_hydb_query(per_capita_sql)
            logger.info(f"人均消费额数据获取完成，共 {len(per_capita_results)} 条记录")

            # 2. 获取客单价数据
            unit_price_sql = MemberLevelsSqlQueries.get_customer_unit_price_sql(start_date, end_date, bid, sid)
            unit_price_results = await db.execute_welife_hydb_query(unit_price_sql)
            logger.info(f"客单价数据获取完成，共 {len(unit_price_results)} 条记录")

            # 3. 获取平均消费频次数据
            frequency_sql = MemberLevelsSqlQueries.get_avg_consumption_frequency_sql(start_date, end_date, bid, sid)
            frequency_results = await db.execute_welife_hydb_query(frequency_sql)
            logger.info(f"平均消费频次数据获取完成，共 {len(frequency_results)} 条记录")

            # 合并数据
            level_data = self._merge_level_data(per_capita_results, unit_price_results, frequency_results)

            logger.info(f"会员等级消费数据合并完成，共 {len(level_data)} 个等级")
            return level_data

        except Exception as e:
            logger.error(f"获取会员等级消费数据失败: {e}")
            import traceback
            traceback.print_exc()
            return []

    def _merge_level_data(self, per_capita_results: List[Dict], unit_price_results: List[Dict], frequency_results: List[Dict]) -> List[Dict[str, Any]]:
        """
        合并三个查询的结果数据

        Args:
            per_capita_results: 人均消费额查询结果
            unit_price_results: 客单价查询结果
            frequency_results: 平均消费频次查询结果

        Returns:
            List: 合并后的会员等级数据（限制前10个等级）
        """
        try:
            # 创建字典以便快速查找
            per_capita_dict = {item.get('ccName', '未知等级'): float(item.get('perCapitaConsumption', 0)) for item in per_capita_results}
            unit_price_dict = {item.get('ccName', '未知等级'): float(item.get('customerUnitPrice', 0)) for item in unit_price_results}
            frequency_dict = {item.get('ccName', '未知等级'): float(item.get('avgConsumFrequency', 0)) for item in frequency_results}

            # 获取所有会员等级名称
            all_levels = set()
            all_levels.update(per_capita_dict.keys())
            all_levels.update(unit_price_dict.keys())
            all_levels.update(frequency_dict.keys())

            # 合并数据
            merged_data = []
            for level_name in all_levels:
                merged_data.append({
                    'ccName': level_name,
                    'perCapitaConsumption': per_capita_dict.get(level_name, 0.0),
                    'customerUnitPrice': unit_price_dict.get(level_name, 0.0),
                    'avgConsumFrequency': frequency_dict.get(level_name, 0.0)
                })

            logger.info(f"数据合并完成，原始数据包含 {len(merged_data)} 个等级")

            # 按人均消费额降序排序，取前10个等级
            merged_data.sort(key=lambda x: x['perCapitaConsumption'], reverse=True)
            top_10_data = merged_data[:10]

            logger.info(f"限制为前10个等级，最终包含 {len(top_10_data)} 个等级: {[item['ccName'] for item in top_10_data]}")
            return top_10_data

        except Exception as e:
            logger.error(f"合并会员等级数据失败: {e}")
            return []

    async def _generate_chart(self, data: List[Dict[str, Any]], title: str, image_type: str) -> str:
        """
        生成会员等级消费分析混合图表

        Args:
            data: 会员等级数据
            title: 图表标题
            image_type: 图片类型

        Returns:
            str: 图片保存路径
        """
        try:
            if not data:
                logger.warning("数据为空，生成空数据提示图表")
                return self._generate_empty_data_chart(title, image_type)

            # 提取数据
            level_names = [item['ccName'] for item in data]
            per_capita_consumption = [item['perCapitaConsumption'] for item in data]
            customer_unit_price = [item['customerUnitPrice'] for item in data]
            avg_frequency = [item['avgConsumFrequency'] for item in data]

            logger.info(f"准备生成图表，包含 {len(level_names)} 个会员等级")

            # 创建图表
            fig, ax1 = plt.subplots(figsize=(14, 8))

            # 设置X轴位置
            x_pos = range(len(level_names))
            x = np.arange(len(level_names))

            # 绘制柱状图（左Y轴 - 金额）
            bar_width = 0.25

            # 绘制柱状图（人均消费额和客单价）
            bars1 = ax1.bar([x - bar_width for x in x_pos], per_capita_consumption,
                           bar_width, label='人均消费额', color='#4472C4', alpha=0.8)
            bars2 = ax1.bar([x + bar_width for x in x_pos], customer_unit_price,
                           bar_width, label='客单价', color='#E74C3C', alpha=0.8)

            # 设置左Y轴
            ax1.set_xlabel('会员卡名称', fontsize=12)
            ax1.set_ylabel('消费金额（元）', fontsize=12)
            ax1.set_title(title, fontsize=16, fontweight='bold', pad=20)
            ax1.set_xticks(x_pos)
            ax1.set_xticklabels(level_names, rotation=0, ha='center')
            ax1.grid(True, alpha=0.3)

            # 创建右Y轴（平均消费频次）
            ax2 = ax1.twinx()

            # 绘制折线图（平均消费频次）
            line1 = ax2.plot(x_pos, avg_frequency, 'o-', color='#000000', linewidth=2,
                            markersize=6, label='平均消费频次')

            # 设置右Y轴
            ax2.set_ylabel('平均消费频次（次）', fontsize=12)
            # 设置右Y轴范围，处理全零数据的情况
            max_frequency = max(avg_frequency) if avg_frequency and max(avg_frequency) > 0 else 1
            ax2.set_ylim(0, max_frequency * 1.2)

            # 在柱状图上添加数值标签，处理全零数据的情况
            max_per_capita = max(per_capita_consumption) if per_capita_consumption and max(per_capita_consumption) > 0 else 10
            max_unit_price = max(customer_unit_price) if customer_unit_price and max(customer_unit_price) > 0 else 10
            max_bar_height = max(max_per_capita, max_unit_price)

            for i, (per_capita, unit_price) in enumerate(zip(per_capita_consumption, customer_unit_price)):
                ax1.text(i - bar_width, per_capita + max_bar_height * 0.02, f'{per_capita:.2f}',
                        ha='center', va='bottom', fontsize=9)
                ax1.text(i + bar_width, unit_price + max_bar_height * 0.02, f'{unit_price:.2f}',
                        ha='center', va='bottom', fontsize=9)

            # 在折线图上添加数值标签，处理全零数据的情况
            max_frequency = max(avg_frequency) if avg_frequency and max(avg_frequency) > 0 else 1
            for i, frequency in enumerate(avg_frequency):
                ax2.text(i, frequency + max_frequency * 0.05, f'{frequency:.2f}',
                        ha='center', va='bottom', fontsize=10, color='#000000', fontweight='bold')

            # 合并图例
            lines1, labels1 = ax1.get_legend_handles_labels()
            lines2, labels2 = ax2.get_legend_handles_labels()
            ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper left', fontsize=11)

            # 添加数据表格
            self._add_data_table(data, ax1)

            # 调整布局
            plt.tight_layout()

            # 保存图片
            file_path = self.image_manager.get_image_path(image_type)
            plt.savefig(file_path, dpi=300, bbox_inches='tight',
                       facecolor='white', edgecolor='none')
            plt.close(fig)

            logger.info(f"会员等级消费分析图表生成完成: {file_path}")
            return file_path

        except Exception as e:
            logger.error(f"生成会员等级消费分析图表失败 {image_type}: {e}")
            import traceback
            traceback.print_exc()
            return ""

    def _add_data_table(self, data: List[Dict[str, Any]], ax):
        """
        在图表下方添加数据表格（横向布局，会员等级为横轴）

        Args:
            data: 数据列表
            ax: 坐标轴对象
        """
        try:
            if not data:
                return

            # 准备横向表格数据
            level_names = [item['ccName'] for item in data]
            per_capita_values = [f"{item['perCapitaConsumption']:.2f}" for item in data]
            unit_price_values = [f"{item['customerUnitPrice']:.2f}" for item in data]
            frequency_values = [f"{item['avgConsumFrequency']:.2f}" for item in data]

            # 构建横向表格数据：第一行是会员等级名称，后面三行是数据
            table_data = [
                level_names,  # 第一行：会员卡名称
                per_capita_values,  # 第二行：人均消费额
                unit_price_values,  # 第三行：客单价
                frequency_values  # 第四行：平均消费频次
            ]

            # 行标题（左侧标签）
            row_labels = ['会员卡名称', '人均消费额(元)', '客单价(元)', '平均消费频次(次)']

            # 创建表格
            table = ax.table(
                cellText=table_data,
                rowLabels=row_labels,
                cellLoc='center',
                loc='bottom',
                bbox=[0, -0.8, 1, 0.6]  # [x, y, width, height] - 匹配 AvgConsumptionPic.py 的布局
            )

            # 设置表格样式
            table.auto_set_font_size(False)
            table.set_fontsize(7)  # 匹配 AvgConsumptionPic.py 的字体大小
            table.scale(1, 1.2)

            # 设置行标题样式（左侧列）
            for i in range(len(row_labels)):
                table[(i, -1)].set_facecolor('#4472C4')
                table[(i, -1)].set_text_props(weight='bold', color='white')

            # 设置数据单元格样式
            for i in range(len(row_labels)):
                for j in range(len(level_names)):
                    if i == 0:  # 会员等级名称行使用浅蓝色
                        table[(i, j)].set_facecolor('#E7F3FF')
                        table[(i, j)].set_text_props(weight='bold')
                    elif i % 2 == 1:  # 奇数行使用浅灰色
                        table[(i, j)].set_facecolor('#F8F9FA')
                    else:  # 偶数行使用白色
                        table[(i, j)].set_facecolor('white')

            logger.info(f"横向数据表格创建完成，包含 {len(level_names)} 个会员等级的数据")

        except Exception as e:
            logger.error(f"添加数据表格失败: {e}")

    def _generate_empty_data_chart(self, title: str, image_type: str) -> str:
        """
        生成空数据提示图表

        Args:
            title: 图表标题
            image_type: 图片类型

        Returns:
            str: 图片保存路径
        """
        try:
            # 创建空数据图表
            fig, ax = plt.subplots(figsize=(14, 8))

            # 显示"暂无数据"提示
            ax.text(0.5, 0.5, '暂无会员等级消费数据\n请检查数据源或调整查询条件',
                   horizontalalignment='center', verticalalignment='center',
                   fontsize=18, color='#666666', weight='bold',
                   transform=ax.transAxes)

            # 设置标题
            ax.set_title(title, fontsize=16, fontweight='bold', pad=20)

            # 隐藏坐标轴
            ax.set_xlim(0, 1)
            ax.set_ylim(0, 1)
            ax.axis('off')

            # 保存图片
            save_path = self.image_manager.get_image_path(image_type)
            plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close(fig)

            logger.info(f"空数据图表生成完成: {save_path}")
            return save_path

        except Exception as e:
            logger.error(f"生成空数据图表失败: {e}")
            return ""


# 工厂函数
def create_level_consumption_pic_generator(bid: str, image_manager) -> LevelConsumptionPicGenerator:
    """
    创建会员等级消费分析图片生成器

    Args:
        bid: 品牌ID
        image_manager: 图片管理器实例

    Returns:
        LevelConsumptionPicGenerator: 图片生成器实例
    """
    return LevelConsumptionPicGenerator(bid, image_manager)