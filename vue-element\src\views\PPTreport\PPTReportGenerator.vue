<template>
  <div class="ppt-report-generator">
    <!-- 数据筛选区域 -->
    <el-card class="query-form-card" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>
            <el-icon><Document /></el-icon>
            PPT报告生成器
          </h3>
        </div>
      </template>
      
      <el-form ref="queryForm" :model="queryParams" :rules="rules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="查询类型" prop="queryType">
              <el-select v-model="queryParams.queryType" placeholder="请选择查询类型" @change="handleQueryTypeChange">
                <el-option label="周分析" value="week" />
                <el-option label="月分析" value="month" />
                <el-option label="季度分析" value="quarter" />
                <el-option label="半年分析" value="halfyear" />
                <el-option label="自定义查询" value="custom" />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :span="8">
            <el-form-item label="品牌ID" prop="bid">
              <el-input v-model="queryParams.bid" placeholder="请输入品牌ID（必填）" />
            </el-form-item>
          </el-col>
          
          <el-col :span="8">
            <el-form-item label="门店ID" prop="sid">
              <el-input v-model="queryParams.sid" placeholder="请输入门店ID（选填）" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="开始日期" prop="startDate">
              <el-date-picker
                v-model="queryParams.startDate"
                type="date"
                placeholder="请选择开始日期"
                :disabled-date="disabledDate"
              />
            </el-form-item>
          </el-col>

          <el-col :span="8" v-if="queryParams.queryType === 'custom'">
            <el-form-item label="结束日期" prop="endDate">
              <el-date-picker
                v-model="queryParams.endDate"
                type="date"
                placeholder="请选择结束日期"
                :disabled-date="disabledEndDate"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 品智收银筛选条件 -->
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="收银系统" prop="cashierSystem">
              <el-select
                v-model="queryParams.cashierSystem"
                placeholder="请选择收银系统"
                @change="handleCashierSystemChange"
              >
                <el-option label="0-无收银系统" value="0" />
                <el-option
                  label="1-品智收银"
                  value="1"
                  :disabled="!canSelectPinzhi"
                />
              </el-select>
              <!-- 🔥 新增：品智收银验证提示 -->
              <div v-if="queryParams.bid && !canSelectPinzhi" class="validation-tip">
                <el-text type="warning" size="small">
                  当前品牌ID不支持品智收银系统
                </el-text>
              </div>
            </el-form-item>
          </el-col>

          <el-col :span="8" v-if="queryParams.cashierSystem === '1'">
            <el-form-item label="商户选择" prop="merchantId">
              <el-select
                v-model="queryParams.merchantId"
                placeholder="请选择商户"
                filterable
                clearable
              >
                <!-- 🔥 修改：使用过滤后的门店选项，只显示与当前bid匹配的商户 -->
                <el-option
                  v-for="store in filteredStoreOptions"
                  :key="store.value"
                  :label="store.label"
                  :value="store.value"
                />
              </el-select>
              <!-- 🔥 新增：当没有匹配的商户时显示提示 -->
              <div v-if="queryParams.bid && filteredStoreOptions.length === 0" class="validation-tip">
                <el-text type="warning" size="small">
                  当前品牌ID没有对应的品智收银商户
                </el-text>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      
      <!-- 日期限制提示 -->
      <div v-if="dateLimit" class="date-limit-tip">
        <el-alert :title="dateLimit.label" type="info" :closable="false" show-icon />
      </div>
    </el-card>

    <!-- PPT生成区域 -->
    <el-card class="ppt-generate-card" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>
            <el-icon><Document /></el-icon>
            PPT报告生成
          </h3>
        </div>
      </template>

      <div class="generate-content">
        <div class="generate-info">
          <p class="info-text">
            <el-icon><InfoFilled /></el-icon>
            将根据上述查询条件生成包含会员基础数据、消费数据、充值数据和券交易数据的综合分析报告
          </p>
        </div>

        <div class="generate-actions">
          <el-button
            type="primary"
            size="large"
            @click="generatePPT"
            :loading="generating"
            :disabled="!canGenerate"
            class="generate-btn"
          >
            <el-icon><DocumentAdd /></el-icon>
            {{ generating ? '正在生成PPT...' : '生成PPT报告' }}
          </el-button>

          <el-button
            type="info"
            size="large"
            @click="clearAllCache"
            :disabled="generating"
            class="clear-cache-btn"
            plain
          >
            <el-icon><Delete /></el-icon>
            清除缓存
          </el-button>
        </div>

        <!-- 生成状态 -->
        <div v-if="generating || generatedFile" class="status-section">
          <div class="status-info">
            <el-tag
              :type="statusType"
              size="large"
              class="status-tag"
            >
              {{ currentStatus }}
            </el-tag>
          </div>

          <!-- 生成成功后的下载区域 -->
          <div v-if="generatedFile" class="download-section">
            <div class="file-info">
              <p><el-icon><Document /></el-icon> 文件大小: {{ generatedFile.fileSize }}</p>
              <p><el-icon><Files /></el-icon> 幻灯片数: {{ generatedFile.slideCount }}</p>
              <p><el-icon><Clock /></el-icon> 生成时间: {{ formatTime(generatedFile.generatedAt) }}</p>
            </div>
            <el-button
              type="success"
              size="large"
              @click="downloadPPT"
              class="download-btn"
            >
              <el-icon><Download /></el-icon>
              下载PPT报告
            </el-button>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Document,
  DocumentAdd,
  Download,
  InfoFilled,
  Files,
  Clock,
  Delete
} from '@element-plus/icons-vue'
import {
  generationStatus
} from '@/data/samplePPTreport'
import { getStoreOptions, validateBidForPinzhi, checkBidForPinzhi, getBrandInfoByBid } from '@/utils/PinzhiStoreMapping'

export default {
  name: 'PPTReportGenerator',
  components: {
    Document,
    DocumentAdd,
    Download,
    InfoFilled,
    Files,
    Clock
  },
  setup() {
    const generating = ref(false)
    const generatedFile = ref(null)
    const dateLimit = ref(null)

    // localStorage 键名
    const STORAGE_KEYS = {
      queryParams: 'ppt_query_params',
      generatedFile: 'ppt_generated_file'
    }

    // localStorage 工具函数
    const saveToStorage = (key, data) => {
      try {
        localStorage.setItem(key, JSON.stringify(data))
      } catch (error) {
        console.warn('保存数据到localStorage失败:', error)
      }
    }

    const loadFromStorage = (key) => {
      try {
        const data = localStorage.getItem(key)
        return data ? JSON.parse(data) : null
      } catch (error) {
        console.warn('从localStorage读取数据失败:', error)
        return null
      }
    }

    const clearStorage = (key) => {
      try {
        localStorage.removeItem(key)
      } catch (error) {
        console.warn('清除localStorage数据失败:', error)
      }
    }

    // 查询参数（与MemberDataQuery相同）
    const queryParams = reactive({
      queryType: 'week',
      bid: '',
      sid: '',
      startDate: null,
      endDate: null,
      cashierSystem: '0',  // 默认无收银系统
      merchantId: ''       // 商户ID
    })

    // 门店选项
    const storeOptions = ref(getStoreOptions())

    // 🔥 新增：品智收银验证计算属性
    const canSelectPinzhi = computed(() => {
      if (!queryParams.bid) {
        return false  // 没有输入bid时不能选择品智收银
      }
      return validateBidForPinzhi(queryParams.bid)
    })

    // 🔥 新增：过滤后的门店选项（只显示与当前bid匹配的商户）
    const filteredStoreOptions = computed(() => {
      if (!queryParams.bid || queryParams.cashierSystem !== '1') {
        return storeOptions.value  // 非品智收银模式时显示所有选项
      }

      const brandInfo = getBrandInfoByBid(queryParams.bid)
      if (!brandInfo) {
        return []  // 如果bid无效，不显示任何选项
      }

      // 只返回与当前bid匹配的商户选项
      return storeOptions.value.filter(store => store.value === brandInfo.pinyinName)
    })

    // 🔥 新增：监听bid变化，自动选择对应的品智品牌
    watch(() => queryParams.bid, (newBid) => {
      if (newBid && validateBidForPinzhi(newBid)) {
        // 如果输入的bid支持品智收银，自动选择品智收银并设置对应的商户
        const brandInfo = getBrandInfoByBid(newBid)
        if (brandInfo) {
          queryParams.cashierSystem = '1'  // 自动选择品智收银
          queryParams.merchantId = brandInfo.pinyinName  // 自动选择对应的商户
          console.log(`PPT页面自动选择品智收银: ${brandInfo.chineseName} (${brandInfo.pinyinName})`)
        }
      } else if (newBid && !validateBidForPinzhi(newBid)) {
        // 如果输入的bid不支持品智收银，自动切换到无收银系统
        if (queryParams.cashierSystem === '1') {
          queryParams.cashierSystem = '0'
          queryParams.merchantId = ''
          console.log('PPT页面bid不支持品智收银，自动切换到无收银系统')
        }
      }
    })

    // 表单验证规则
    const rules = {
      queryType: [
        { required: true, message: '请选择查询类型', trigger: 'change' }
      ],
      bid: [
        { required: true, message: '请输入品牌ID', trigger: 'blur' }
      ],
      startDate: [
        { required: true, message: '请选择开始日期', trigger: 'change' }
      ]
    }
    
    // 计算属性
    const canGenerate = computed(() => {
      // 基础验证
      if (!queryParams.bid || !queryParams.startDate) {
        return false
      }

      // 品智收银系统验证
      if (queryParams.cashierSystem === '1') {
        // 🔥 新增：验证bid是否支持品智收银
        if (!validateBidForPinzhi(queryParams.bid)) {
          return false
        }

        if (!queryParams.merchantId) {
          return false
        }
      }

      return true
    })

    const currentStatus = computed(() => {
      if (generating.value) return generationStatus.generating
      if (generatedFile.value) return generationStatus.success
      return generationStatus.idle
    })

    const statusType = computed(() => {
      if (generating.value) return 'warning'
      if (generatedFile.value) return 'success'
      return 'info'
    })
    
    // 方法
    const handleQueryTypeChange = (value) => {
      // 处理查询类型变化的逻辑（与MemberDataQuery相同）
      if (value !== 'custom') {
        queryParams.endDate = null
      }
    }

    // 处理收银系统变化
    const handleCashierSystemChange = (value) => {
      // 当切换收银系统时，清空商户选择
      queryParams.merchantId = ''

      // 如果切换到品智收银，进行数据获取准备
      if (value === '1') {
        console.log('切换到品智收银系统')
        ElMessage.info('已选择品智收银系统，请选择商户后即可生成包含品智收银数据的PPT报告')
      } else {
        console.log('切换到无收银系统')
        ElMessage.info('已切换到无收银系统模式')
      }
    }
    
    const disabledDate = (time) => {
      return time.getTime() > Date.now()
    }
    
    const disabledEndDate = (time) => {
      if (!queryParams.startDate) return time.getTime() > Date.now()
      return time.getTime() > Date.now() || time.getTime() < queryParams.startDate.getTime()
    }
    
    const generatePPT = async () => {
      if (!canGenerate.value) {
        ElMessage.warning('请完善所有必填配置项')
        return
      }

      generating.value = true
      generatedFile.value = null

      try {
        // 构建API请求数据
        const requestData = {
          query_type: queryParams.queryType,
          bid: queryParams.bid,
          sid: queryParams.sid || null,
          start_date: queryParams.startDate ? formatDateForAPI(queryParams.startDate) : null,
          end_date: queryParams.endDate ? formatDateForAPI(queryParams.endDate) : null,
          cashier_system: queryParams.cashierSystem || '0',
          merchant_id: queryParams.merchantId || null
        }

        // 如果不是自定义查询，根据查询类型计算结束日期
        if (queryParams.queryType !== 'custom' && queryParams.startDate) {
          const startDate = new Date(queryParams.startDate)
          const endDate = new Date(startDate)

          switch (queryParams.queryType) {
            case 'week':
              endDate.setDate(startDate.getDate() + 6)
              break
            case 'month':
              endDate.setMonth(startDate.getMonth() + 1)
              endDate.setDate(startDate.getDate() - 1)
              break
            case 'quarter':
              endDate.setMonth(startDate.getMonth() + 3)
              endDate.setDate(startDate.getDate() - 1)
              break
            case 'halfyear':
              endDate.setMonth(startDate.getMonth() + 6)
              endDate.setDate(startDate.getDate() - 1)
              break
          }

          requestData.end_date = formatDateForAPI(endDate)
        }

        console.log('发送PPT生成请求:', requestData)

        // 调用真实API
        const response = await fetch('/api/ppt-report/generate', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(requestData)
        })

        const result = await response.json()

        if (result.code === 200 && result.data) {
          generatedFile.value = result.data

          // 保存生成的文件信息到localStorage
          saveToStorage(STORAGE_KEYS.generatedFile, {
            ...result.data,
            generatedAt: new Date().toISOString()
          })

          ElMessage.success('PPT生成成功！')
        } else {
          throw new Error(result.message || 'PPT生成失败')
        }

      } catch (error) {
        ElMessage.error('PPT生成失败，请重试')
        console.error('PPT generation error:', error)
      } finally {
        generating.value = false
      }
    }
    
    const downloadPPT = async () => {
      if (!generatedFile.value || !generatedFile.value.downloadUrl) {
        ElMessage.error('下载链接不可用')
        return
      }

      try {
        ElMessage.info('正在准备下载...')

        // 检查是否是OSS链接
        const isOSSUrl = generatedFile.value.downloadUrl.includes('oss-cn-beijing.aliyuncs.com')

        if (isOSSUrl) {
          // OSS链接直接下载，跳过HEAD检查（OSS预签名URL可能不支持HEAD请求）
          console.log('检测到OSS链接，直接下载:', generatedFile.value.downloadUrl)
        } else {
          // 本地API链接先检查有效性
          const checkResponse = await fetch(generatedFile.value.downloadUrl, { method: 'HEAD' })
          if (!checkResponse.ok) {
            throw new Error(`文件不存在或已过期 (状态码: ${checkResponse.status})`)
          }
        }

        // 使用真实的下载链接
        const link = document.createElement('a')
        link.href = generatedFile.value.downloadUrl
        link.download = generatedFile.value.fileName || `会员分析报告_${new Date().getTime()}.pptx`
        link.target = '_blank'  // 在新窗口打开，避免页面跳转

        // 添加到DOM并触发下载
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        ElMessage.success(`开始下载PPT文件 ${isOSSUrl ? '(OSS云存储)' : '(本地存储)'}`)

        // 记录下载日志
        console.log('PPT下载成功:', {
          fileName: generatedFile.value.fileName,
          downloadUrl: generatedFile.value.downloadUrl,
          fileSize: generatedFile.value.fileSize
        })

      } catch (error) {
        console.error('PPT下载失败:', error)

        const isOSSUrl = generatedFile.value.downloadUrl.includes('oss-cn-beijing.aliyuncs.com')

        if (isOSSUrl) {
          // OSS链接失败，尝试通过本地API下载
          ElMessage.warning(`OSS下载失败: ${error.message}`)
          ElMessage.info('正在尝试本地备用下载方式...')

          // 构造本地API下载链接
          const fileName = generatedFile.value.fileName || generatedFile.value.objectName
          if (fileName) {
            const localDownloadUrl = `/api/ppt-report/download/ppt-reports/${fileName}`
            window.open(localDownloadUrl, '_blank')
          } else {
            ElMessage.error('无法获取文件名，下载失败')
          }
        } else {
          // 本地链接失败
          ElMessage.error(`下载失败: ${error.message}`)
          ElMessage.info('正在尝试备用下载方式...')
          window.open(generatedFile.value.downloadUrl, '_blank')
        }
      }
    }
    
    const formatTime = (timeString) => {
      return new Date(timeString).toLocaleString('zh-CN')
    }

    // 格式化日期为API所需格式 (YYYY-MM-DD)
    const formatDateForAPI = (date) => {
      if (!date) return null

      // 避免时区转换问题，使用本地时间
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')

      return `${year}-${month}-${day}`
    }

    // 数据恢复和持久化
    onMounted(() => {
      console.log('PPT页面加载，尝试恢复数据...')

      // 恢复查询参数
      const savedQueryParams = loadFromStorage(STORAGE_KEYS.queryParams)
      if (savedQueryParams) {
        console.log('恢复查询参数:', savedQueryParams)
        Object.assign(queryParams, {
          queryType: savedQueryParams.queryType || 'week',
          bid: savedQueryParams.bid || '',
          sid: savedQueryParams.sid || '',
          startDate: savedQueryParams.startDate ? new Date(savedQueryParams.startDate) : null,
          endDate: savedQueryParams.endDate ? new Date(savedQueryParams.endDate) : null
        })
        ElMessage.success('已恢复上次的查询条件')
      }

      // 恢复生成的文件信息
      const savedFile = loadFromStorage(STORAGE_KEYS.generatedFile)
      if (savedFile) {
        // 检查文件是否过期（24小时）
        const generatedAt = new Date(savedFile.generatedAt)
        const now = new Date()
        const hoursDiff = (now - generatedAt) / (1000 * 60 * 60)

        if (hoursDiff < 24) {
          console.log('恢复生成的文件信息:', savedFile)
          generatedFile.value = savedFile
          ElMessage.success('已恢复上次生成的PPT文件')
        } else {
          console.log('生成的文件已过期，清除缓存')
          clearStorage(STORAGE_KEYS.generatedFile)
        }
      }
    })

    // 监听查询参数变化，自动保存
    watch(queryParams, (newParams) => {
      console.log('查询参数变化，自动保存:', newParams)
      saveToStorage(STORAGE_KEYS.queryParams, newParams)
    }, { deep: true })

    // 清除所有缓存数据
    const clearAllCache = () => {
      clearStorage(STORAGE_KEYS.queryParams)
      clearStorage(STORAGE_KEYS.generatedFile)

      // 重置组件状态
      Object.assign(queryParams, {
        queryType: 'week',
        bid: '',
        sid: '',
        startDate: null,
        endDate: null
      })
      generatedFile.value = null

      ElMessage.success('已清除所有缓存数据')
    }

    return {
      // 响应式数据
      generating,
      generatedFile,
      queryParams,
      dateLimit,
      rules,
      storeOptions,
      filteredStoreOptions,  // 🔥 新增：过滤后的门店选项

      // 计算属性
      canGenerate,
      canSelectPinzhi,  // 🔥 新增：品智收银验证计算属性
      currentStatus,
      statusType,

      // 方法
      handleQueryTypeChange,
      handleCashierSystemChange,
      disabledDate,
      disabledEndDate,
      generatePPT,
      downloadPPT,
      formatTime,
      clearAllCache
    }
  }
}
</script>

<style scoped>
.ppt-report-generator {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.query-form-card,
.ppt-generate-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #303133;
}

.date-limit-tip {
  margin-top: 15px;
}

/* PPT生成区域样式 */
.generate-content {
  text-align: center;
  padding: 40px 20px;
}

.generate-info {
  margin-bottom: 30px;
}

.info-text {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: #606266;
  font-size: 16px;
  line-height: 1.6;
  margin: 0;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.generate-actions {
  margin-bottom: 30px;
  display: flex;
  gap: 15px;
  align-items: center;
  justify-content: center;
}

.generate-btn {
  padding: 15px 40px;
  font-size: 18px;
  font-weight: 600;
  min-width: 200px;
}

.clear-cache-btn {
  padding: 12px 24px;
  font-size: 14px;
  min-width: 120px;
}

.status-section {
  margin-top: 30px;
  padding-top: 30px;
  border-top: 1px solid #e4e7ed;
}

.status-info {
  margin-bottom: 20px;
}

.status-tag {
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 500;
}

.download-section {
  background: #f8f9fa;
  padding: 25px;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.file-info {
  margin-bottom: 20px;
  text-align: left;
}

.file-info p {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 8px 0;
  color: #606266;
  font-size: 14px;
}

.download-btn {
  padding: 12px 30px;
  font-size: 16px;
  font-weight: 600;
  min-width: 180px;
}



/* 响应式设计 */
@media (max-width: 768px) {
  .ppt-report-generator {
    padding: 10px;
  }

  .generate-content {
    padding: 20px 10px;
  }

  .generate-btn {
    padding: 12px 20px;
    font-size: 16px;
    min-width: 160px;
  }

  .info-text {
    font-size: 14px;
    padding: 15px;
  }

  .download-btn {
    min-width: 140px;
  }
}

/* 🔥 新增：品智收银验证提示样式 */
.validation-tip {
  margin-top: 4px;
  font-size: 12px;
}
</style>
