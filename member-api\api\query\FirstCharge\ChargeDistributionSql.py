import logging
from typing import Optional, List, Dict, Any
from core.database import db

logger = logging.getLogger(__name__)

class ChargeDistributionQueries:
    """充值分布统计SQL查询类"""

    @staticmethod
    async def get_charge_amount_distribution(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取充值档位的笔数统计

        数据库：dwoutput.dprpt_welife_charge_detail
        字段：charge_cash（实收金额）
        计算方式：按charge_cash分组统计笔数，按笔数降序排列

        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)

        Returns:
            充值档位分布列表，每个元素包含charge_cash和charge_count
        """
        try:
            sid_condition = f"AND a.sid = '{sid}'" if sid else ""

            sql = f"""
            SELECT
              a.charge_cash,
              COUNT(*) AS charge_count
            FROM dwoutput.dprpt_welife_charge_detail a
            WHERE a.bid = {bid}
              AND a.tcType = 1
              AND a.ftime BETWEEN {start_date} AND {end_date}
              {sid_condition}
            GROUP BY a.charge_cash
            ORDER BY charge_count DESC
            """

            logger.info(f"执行充值档位分布查询 - bid: {bid}, sid: {sid}, 时间范围: {start_date}-{end_date}")
            logger.debug(f"充值档位分布查询SQL: {sql}")

            result = await db.execute_dwoutput_query(sql)

            # 数据格式转换，确保数值类型正确
            formatted_result = []
            for row in result:
                formatted_result.append({
                    'charge_cash': float(row['charge_cash']) if row['charge_cash'] is not None else 0.0,
                    'charge_count': int(row['charge_count']) if row['charge_count'] is not None else 0
                })

            logger.info(f"充值档位分布查询完成，返回{len(formatted_result)}个档位")
            return formatted_result

        except Exception as e:
            logger.error(f"获取充值档位分布失败: {str(e)}", exc_info=True)
            raise

    @staticmethod
    async def get_total_charge_amount_distribution(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取区间内总储值金额分布图（会员人数）

        数据库：dwoutput.dprpt_welife_charge_detail
        字段：ucid（用户ID）, charge_cash（充值金额）
        计算方式：先按用户汇总总充值金额，再按金额区间分组统计人数
        金额区间：0-199元、200-399元、...、1800-1999元、2000元及以上

        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)

        Returns:
            总储值金额分布列表，每个元素包含total_charge_range和user_count
        """
        try:
            sid_condition = f"AND a.sid = '{sid}'" if sid else ""

            sql = f"""
            SELECT
              CASE
                WHEN total_charge < 200000 THEN
                  CONCAT(FLOOR(total_charge / 20000) * 200, '-', FLOOR(total_charge / 20000) * 200 + 199, '元')
                ELSE '2000元及以上'
              END AS total_charge_range,
              COUNT(*) AS user_count
            FROM (
              SELECT
                a.ucid,
                SUM(a.charge_cash) AS total_charge
              FROM dwoutput.dprpt_welife_charge_detail a
              WHERE a.bid = {bid}
                AND a.tcType = 1
                AND a.ftime BETWEEN {start_date} AND {end_date}
                {sid_condition}
              GROUP BY a.ucid
            ) t
            GROUP BY
              CASE
                WHEN total_charge < 200000 THEN FLOOR(total_charge / 20000)
                ELSE 10  -- 大于等于2000元
              END
            ORDER BY
              CASE
                WHEN total_charge < 200000 THEN FLOOR(total_charge / 20000)
                ELSE 10
              END
            """

            logger.info(f"执行总储值金额分布查询 - bid: {bid}, sid: {sid}, 时间范围: {start_date}-{end_date}")
            logger.debug(f"总储值金额分布查询SQL: {sql}")

            result = await db.execute_dwoutput_query(sql)

            # 数据格式转换，确保数值类型正确
            formatted_result = []
            for row in result:
                formatted_result.append({
                    'total_charge_range': str(row['total_charge_range']) if row['total_charge_range'] is not None else '',
                    'user_count': int(row['user_count']) if row['user_count'] is not None else 0
                })

            logger.info(f"总储值金额分布查询完成，返回{len(formatted_result)}个区间")
            return formatted_result

        except Exception as e:
            logger.error(f"获取总储值金额分布失败: {str(e)}", exc_info=True)
            raise

    @staticmethod
    async def get_charge_frequency_distribution(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取储值次数的会员人数分布

        数据库：dwoutput.dprpt_welife_charge_detail
        字段：ucid（用户ID）
        计算方式：先按用户统计充值次数，再按次数区间分组统计人数
        次数区间：1次、2次、3次、4次、5次、5次以上

        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)

        Returns:
            储值次数分布列表，每个元素包含charge_count_range和user_count
        """
        try:
            sid_condition = f"AND sid = '{sid}'" if sid else ""

            sql = f"""
            SELECT
              CASE
                WHEN charge_times = 1 THEN '1次'
                WHEN charge_times = 2 THEN '2次'
                WHEN charge_times = 3 THEN '3次'
                WHEN charge_times = 4 THEN '4次'
                WHEN charge_times = 5 THEN '5次'
                ELSE '5次以上'
              END AS charge_count_range,
              COUNT(*) AS user_count
            FROM (
              SELECT
                ucid,
                COUNT(*) AS charge_times
              FROM dwoutput.dprpt_welife_charge_detail
              WHERE bid = {bid}
                AND tcType = 1
                AND ftime BETWEEN {start_date} AND {end_date}
                {sid_condition}
              GROUP BY ucid
            ) t
            GROUP BY
              CASE
                WHEN charge_times = 1 THEN '1次'
                WHEN charge_times = 2 THEN '2次'
                WHEN charge_times = 3 THEN '3次'
                WHEN charge_times = 4 THEN '4次'
                WHEN charge_times = 5 THEN '5次'
                ELSE '5次以上'
              END
            ORDER BY
              CASE
                WHEN charge_times = 1 THEN 1
                WHEN charge_times = 2 THEN 2
                WHEN charge_times = 3 THEN 3
                WHEN charge_times = 4 THEN 4
                WHEN charge_times = 5 THEN 5
                ELSE 6
              END
            """

            logger.info(f"执行储值次数分布查询 - bid: {bid}, sid: {sid}, 时间范围: {start_date}-{end_date}")
            logger.debug(f"储值次数分布查询SQL: {sql}")

            result = await db.execute_dwoutput_query(sql)

            # 数据格式转换，确保数值类型正确
            formatted_result = []
            for row in result:
                formatted_result.append({
                    'charge_count_range': str(row['charge_count_range']) if row['charge_count_range'] is not None else '',
                    'user_count': int(row['user_count']) if row['user_count'] is not None else 0
                })

            logger.info(f"储值次数分布查询完成，返回{len(formatted_result)}个次数区间")
            return formatted_result

        except Exception as e:
            logger.error(f"获取储值次数分布失败: {str(e)}", exc_info=True)
            raise