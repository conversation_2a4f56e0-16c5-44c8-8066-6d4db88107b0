"""
会员基础数量统计SQL查询模块
专门处理 dprpt_welife_users_stat 表中的新增会员相关字段查询
"""

from typing import Optional
import logging
from .MemberBaseSql import MemberBaseSqlQueries

logger = logging.getLogger(__name__)

class MemberBaseNumSqlQueries:
    """会员基础数量统计SQL查询类 - 处理 dprpt_welife_users_stat 表的新增会员统计字段"""

    # ========== 求和统计类型的查询函数（时间范围内累加） ==========

    @staticmethod
    def get_dwoutput_total_uincre_num_by_wx_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """获取微信新增会员数查询SQL

        数据库：dprpt_welife_users_stat
        字段：uIncreNumByWx（微信新增会员数）
        计算方式：时间范围内所有天的uIncreNumByWx求和
        说明：统计时间范围内通过微信渠道新增的会员数量

        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)

        Returns:
            微信新增会员数查询SQL
        """
        sid_condition = f"AND sid = '{sid}'" if sid else ""

        return f"""
        SELECT SUM(uIncreNumByWx) AS total_uincre_num_by_wx
        FROM dprpt_welife_users_stat
        WHERE ftime BETWEEN {start_date} AND {end_date}
        AND bid = {bid}
        {sid_condition}
        """

    @staticmethod
    def get_dwoutput_total_uincre_num_by_dy_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """获取抖音新增会员数查询SQL

        数据库：dprpt_welife_users_stat
        字段：uIncreNumByDY（抖音新增会员数）
        计算方式：时间范围内所有天的uIncreNumByDY求和
        说明：统计时间范围内通过抖音渠道新增的会员数量

        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)

        Returns:
            抖音新增会员数查询SQL
        """
        sid_condition = f"AND sid = '{sid}'" if sid else ""

        return f"""
        SELECT SUM(uIncreNumByDY) AS total_uincre_num_by_dy
        FROM dprpt_welife_users_stat
        WHERE ftime BETWEEN {start_date} AND {end_date}
        AND bid = {bid}
        {sid_condition}
        """

    @staticmethod
    def get_dwoutput_total_uincre_num_by_mt_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """获取美团新增会员数查询SQL

        数据库：dprpt_welife_users_stat
        字段：uIncreNumByMT（美团新增会员数）
        计算方式：时间范围内所有天的uIncreNumByMT求和
        说明：统计时间范围内通过美团渠道新增的会员数量

        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)

        Returns:
            美团新增会员数查询SQL
        """
        sid_condition = f"AND sid = '{sid}'" if sid else ""

        return f"""
        SELECT SUM(uIncreNumByMT) AS total_uincre_num_by_mt
        FROM dprpt_welife_users_stat
        WHERE ftime BETWEEN {start_date} AND {end_date}
        AND bid = {bid}
        {sid_condition}
        """

    @staticmethod
    def get_dwoutput_total_uincre_num_by_dp_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """获取大众点评新增会员数查询SQL

        数据库：dprpt_welife_users_stat
        字段：uIncreNumByDP（大众点评新增会员数）
        计算方式：时间范围内所有天的uIncreNumByDP求和
        说明：统计时间范围内通过大众点评渠道新增的会员数量

        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)

        Returns:
            大众点评新增会员数查询SQL
        """
        sid_condition = f"AND sid = '{sid}'" if sid else ""

        return f"""
        SELECT SUM(uIncreNumByDP) AS total_uincre_num_by_dp
        FROM dprpt_welife_users_stat
        WHERE ftime BETWEEN {start_date} AND {end_date}
        AND bid = {bid}
        {sid_condition}
        """

    @staticmethod
    def get_dwoutput_total_uincre_num_by_zfbhy_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """获取支付宝会员卡小程序新增会员量查询SQL

        数据库：dprpt_welife_users_stat
        字段：uIncreNumByZFBHY（支付宝会员卡小程序新增会员量）
        计算方式：时间范围内所有天的uIncreNumByZFBHY求和
        说明：统计时间范围内通过支付宝会员卡小程序渠道新增的会员数量

        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)

        Returns:
            支付宝会员卡小程序新增会员量查询SQL
        """
        sid_condition = f"AND sid = '{sid}'" if sid else ""

        return f"""
        SELECT SUM(uIncreNumByZFBHY) AS total_uincre_num_by_zfbhy
        FROM dprpt_welife_users_stat
        WHERE ftime BETWEEN {start_date} AND {end_date}
        AND bid = {bid}
        {sid_condition}
        """

    @staticmethod
    def get_dwoutput_total_uincre_num_by_hm_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """获取鸿蒙应用新增会员量查询SQL

        数据库：dprpt_welife_users_stat
        字段：uIncreNumByHM（鸿蒙应用新增会员量）
        计算方式：时间范围内所有天的uIncreNumByHM求和
        说明：统计时间范围内通过鸿蒙应用渠道新增的会员数量

        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)

        Returns:
            鸿蒙应用新增会员量查询SQL
        """
        sid_condition = f"AND sid = '{sid}'" if sid else ""

        return f"""
        SELECT SUM(uIncreNumByHM) AS total_uincre_num_by_hm
        FROM dprpt_welife_users_stat
        WHERE ftime BETWEEN {start_date} AND {end_date}
        AND bid = {bid}
        {sid_condition}
        """

    @staticmethod
    def get_dwoutput_total_uincre_num_by_zfb_sql(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> str:
        """获取支付宝新增会员数查询SQL

        数据库：dprpt_welife_users_stat
        字段：uIncreNumByZFB（支付宝新增会员数）
        计算方式：时间范围内所有天的uIncreNumByZFB求和
        说明：统计时间范围内通过支付宝渠道新增的会员数量

        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)

        Returns:
            支付宝新增会员数查询SQL
        """
        sid_condition = f"AND sid = '{sid}'" if sid else ""

        return f"""
        SELECT SUM(uIncreNumByZFB) AS total_uincre_num_by_zfb
        FROM dprpt_welife_users_stat
        WHERE ftime BETWEEN {start_date} AND {end_date}
        AND bid = {bid}
        {sid_condition}
        """

