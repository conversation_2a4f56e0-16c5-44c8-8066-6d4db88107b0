import logging
from typing import Optional
from core.database import db

logger = logging.getLogger(__name__)

class CreditCountQueries:
    """积分统计SQL查询类"""

    @staticmethod
    async def get_total_credit_reward(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> int:
        """获取赠送积分数量统计

        数据库：welife_hydb.dprpt_mobile_users_report
        字段：credit_reward（赠送积分数量）
        计算方式：时间范围内所有记录的credit_reward求和

        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)

        Returns:
            赠送积分总数量
        """
        try:
            sid_condition = f"AND sid = '{sid}'" if sid else ""

            sql = f"""
            SELECT SUM(COALESCE(credit_reward, 0)) AS total_credit_reward
            FROM welife_hydb.dprpt_mobile_users_report
            WHERE bid = {bid}
              AND ftime BETWEEN {start_date} AND {end_date}
              {sid_condition}
            """

            logger.info(f"执行赠送积分统计查询 - bid: {bid}, sid: {sid}, 时间范围: {start_date}-{end_date}")
            logger.debug(f"赠送积分统计查询SQL: {sql}")

            result = await db.execute_welife_hydb_one(sql)
            count = result.get('total_credit_reward', 0) if result else 0
            # 处理None值
            count = int(count) if count is not None else 0

            logger.info(f"赠送积分统计查询完成，结果: {count}")
            return count

        except Exception as e:
            logger.error(f"获取赠送积分统计失败: {str(e)}", exc_info=True)
            raise

    @staticmethod
    async def get_total_credit_consume(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> int:
        """获取使用积分数量统计

        数据库：welife_hydb.dprpt_mobile_users_report
        字段：credit_consume（使用积分数量）
        计算方式：时间范围内所有记录的credit_consume求和

        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)

        Returns:
            使用积分总数量
        """
        try:
            sid_condition = f"AND sid = '{sid}'" if sid else ""

            sql = f"""
            SELECT SUM(COALESCE(credit_consume, 0)) AS total_credit_consume
            FROM welife_hydb.dprpt_mobile_users_report
            WHERE bid = {bid}
              AND ftime BETWEEN {start_date} AND {end_date}
              {sid_condition}
            """

            logger.info(f"执行使用积分统计查询 - bid: {bid}, sid: {sid}, 时间范围: {start_date}-{end_date}")
            logger.debug(f"使用积分统计查询SQL: {sql}")

            result = await db.execute_welife_hydb_one(sql)
            count = result.get('total_credit_consume', 0) if result else 0
            # 处理None值
            count = int(count) if count is not None else 0

            logger.info(f"使用积分统计查询完成，结果: {count}")
            return count

        except Exception as e:
            logger.error(f"获取使用积分统计失败: {str(e)}", exc_info=True)
            raise