#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
积分相关函数测试脚本

测试 CreditCountSql.py 和 CreditStatisticsSql.py 中的积分查询函数：

CreditCountSql.py:
1. get_total_credit_reward - 赠送积分数量统计
2. get_total_credit_consume - 使用积分数量统计

CreditStatisticsSql.py:
3. get_credit_balance_distribution - 会员积分余额分布
4. get_credit_balance_by_level - 每个卡等级的积分总余额
5. get_credit_balance_by_age - 各年龄段积分余额分布

使用方法:
python test_credit_functions.py
"""

import asyncio
import logging
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from api.query.Credit.CreditCountSql import CreditCountQueries
from api.query.Credit.CreditStatisticsSql import CreditStatisticsQueries
from core.database import db
from utils.setup_logging import setup_logging

# 设置日志
setup_logging()
logger = logging.getLogger(__name__)

class CreditTestRunner:
    """积分函数测试运行器"""
    
    def __init__(self):
        self.test_cases = [
            {
                "name": "测试案例1 - bid=3064710828（主要测试）",
                "bid": "3064710828",
                "start_date": "20250601",
                "end_date": "20250630",
                "sid": None
            },
            {
                "name": "测试案例2 - 带sid过滤",
                "bid": "3064710828",
                "start_date": "20250601", 
                "end_date": "20250630",
                "sid": "4173789868"
            },
            {
                "name": "测试案例3 - 其他bid测试",
                "bid": "1113803514",
                "start_date": "20250601",
                "end_date": "20250630",
                "sid": None
            }
        ]
    
    async def test_total_credit_reward(self, bid: str, start_date: str, end_date: str, sid: str = None):
        """测试赠送积分统计函数"""
        try:
            logger.info(f"=== 测试赠送积分统计 ===")
            logger.info(f"参数: bid={bid}, start_date={start_date}, end_date={end_date}, sid={sid}")
            
            result = await CreditCountQueries.get_total_credit_reward(start_date, end_date, bid, sid)
            
            logger.info(f"赠送积分统计结果: {result}")
            logger.info(f"结果类型: {type(result)}")
            
            # 验证结果
            assert isinstance(result, int), f"期望返回int类型，实际返回{type(result)}"
            assert result >= 0, f"期望非负数，实际返回{result}"
            
            logger.info("✅ 赠送积分统计测试通过")
            return result
            
        except Exception as e:
            logger.error(f"❌ 赠送积分统计测试失败: {str(e)}", exc_info=True)
            raise
    
    async def test_total_credit_consume(self, bid: str, start_date: str, end_date: str, sid: str = None):
        """测试使用积分统计函数"""
        try:
            logger.info(f"=== 测试使用积分统计 ===")
            logger.info(f"参数: bid={bid}, start_date={start_date}, end_date={end_date}, sid={sid}")
            
            result = await CreditCountQueries.get_total_credit_consume(start_date, end_date, bid, sid)
            
            logger.info(f"使用积分统计结果: {result}")
            logger.info(f"结果类型: {type(result)}")
            
            # 验证结果
            assert isinstance(result, int), f"期望返回int类型，实际返回{type(result)}"
            assert result >= 0, f"期望非负数，实际返回{result}"
            
            logger.info("✅ 使用积分统计测试通过")
            return result
            
        except Exception as e:
            logger.error(f"❌ 使用积分统计测试失败: {str(e)}", exc_info=True)
            raise
    
    async def test_credit_balance_distribution(self, bid: str, end_date: str):
        """测试积分余额分布函数"""
        try:
            logger.info(f"=== 测试积分余额分布 ===")
            logger.info(f"参数: bid={bid}, end_date={end_date}")

            result = await CreditStatisticsQueries.get_credit_balance_distribution(end_date, bid)
            
            logger.info(f"积分余额分布结果数量: {len(result)}")
            logger.info(f"结果类型: {type(result)}")
            
            # 验证结果格式
            assert isinstance(result, list), f"期望返回list类型，实际返回{type(result)}"
            
            if result:
                # 验证第一条记录的格式
                first_record = result[0]
                assert isinstance(first_record, dict), f"期望记录为dict类型，实际为{type(first_record)}"
                assert 'credit_range' in first_record, "记录中缺少credit_range字段"
                assert 'user_count_distinct' in first_record, "记录中缺少user_count_distinct字段"
                
                # 显示所有记录
                logger.info("积分余额分布记录:")
                total_users = 0
                for i, record in enumerate(result):
                    logger.info(f"  {i+1}. 积分区间: {record['credit_range']}, 会员人数: {record['user_count_distinct']}")
                    total_users += record['user_count_distinct']
                logger.info(f"总会员人数: {total_users}")
            
            logger.info("✅ 积分余额分布测试通过")
            return result
            
        except Exception as e:
            logger.error(f"❌ 积分余额分布测试失败: {str(e)}", exc_info=True)
            raise
    
    async def test_credit_balance_by_level(self, bid: str, end_date: str):
        """测试卡等级积分余额函数"""
        try:
            logger.info(f"=== 测试卡等级积分余额 ===")
            logger.info(f"参数: bid={bid}, end_date={end_date}")

            result = await CreditStatisticsQueries.get_credit_balance_by_level(end_date, bid)
            
            logger.info(f"卡等级积分余额结果数量: {len(result)}")
            logger.info(f"结果类型: {type(result)}")
            
            # 验证结果格式
            assert isinstance(result, list), f"期望返回list类型，实际返回{type(result)}"
            
            if result:
                # 验证第一条记录的格式
                first_record = result[0]
                assert isinstance(first_record, dict), f"期望记录为dict类型，实际为{type(first_record)}"
                assert 'ccname' in first_record, "记录中缺少ccname字段"
                assert 'total_credit_saving' in first_record, "记录中缺少total_credit_saving字段"
                assert 'user_count' in first_record, "记录中缺少user_count字段"
                
                # 显示前10条记录
                logger.info("卡等级积分余额记录（前10条）:")
                for i, record in enumerate(result[:10]):
                    logger.info(f"  {i+1}. 卡等级: {record['ccname']}, 积分余额: {record['total_credit_saving']}, 会员数: {record['user_count']}")
            
            logger.info("✅ 卡等级积分余额测试通过")
            return result
            
        except Exception as e:
            logger.error(f"❌ 卡等级积分余额测试失败: {str(e)}", exc_info=True)
            raise
    
    async def test_credit_balance_by_age(self, bid: str, end_date: str):
        """测试年龄段积分余额分布函数"""
        try:
            logger.info(f"=== 测试年龄段积分余额分布 ===")
            logger.info(f"参数: bid={bid}, end_date={end_date}")

            result = await CreditStatisticsQueries.get_credit_balance_by_age(end_date, bid)
            
            logger.info(f"年龄段积分余额分布结果数量: {len(result)}")
            logger.info(f"结果类型: {type(result)}")
            
            # 验证结果格式
            assert isinstance(result, list), f"期望返回list类型，实际返回{type(result)}"
            
            if result:
                # 验证第一条记录的格式
                first_record = result[0]
                assert isinstance(first_record, dict), f"期望记录为dict类型，实际为{type(first_record)}"
                assert 'age_range' in first_record, "记录中缺少age_range字段"
                assert 'user_count' in first_record, "记录中缺少user_count字段"
                assert 'total_credit_saving' in first_record, "记录中缺少total_credit_saving字段"
                
                # 显示所有记录
                logger.info("年龄段积分余额分布记录:")
                total_users = 0
                total_credits = 0
                for i, record in enumerate(result):
                    logger.info(f"  {i+1}. 年龄段: {record['age_range']}, 会员数: {record['user_count']}, 积分余额: {record['total_credit_saving']}")
                    total_users += record['user_count']
                    total_credits += record['total_credit_saving']
                logger.info(f"总会员人数: {total_users}, 总积分余额: {total_credits}")
            
            logger.info("✅ 年龄段积分余额分布测试通过")
            return result
            
        except Exception as e:
            logger.error(f"❌ 年龄段积分余额分布测试失败: {str(e)}", exc_info=True)
            raise
    
    async def run_single_test_case(self, test_case):
        """运行单个测试案例"""
        logger.info(f"\n{'='*60}")
        logger.info(f"开始执行: {test_case['name']}")
        logger.info(f"{'='*60}")
        
        bid = test_case['bid']
        start_date = test_case['start_date']
        end_date = test_case['end_date']
        sid = test_case['sid']
        
        results = {}
        
        try:
            # 测试积分统计函数（需要时间范围）
            results['total_credit_reward'] = await self.test_total_credit_reward(bid, start_date, end_date, sid)
            results['total_credit_consume'] = await self.test_total_credit_consume(bid, start_date, end_date, sid)
            
            # 测试积分分布函数（只需要截止日期）
            results['credit_balance_distribution'] = await self.test_credit_balance_distribution(bid, end_date)
            results['credit_balance_by_level'] = await self.test_credit_balance_by_level(bid, end_date)
            results['credit_balance_by_age'] = await self.test_credit_balance_by_age(bid, end_date)
            
            # 输出汇总结果
            logger.info(f"\n{'='*40}")
            logger.info(f"{test_case['name']} - 测试结果汇总:")
            logger.info(f"{'='*40}")
            logger.info(f"赠送积分总数: {results['total_credit_reward']}")
            logger.info(f"使用积分总数: {results['total_credit_consume']}")
            logger.info(f"积分余额分布区间数: {len(results['credit_balance_distribution'])}")
            logger.info(f"卡等级数量: {len(results['credit_balance_by_level'])}")
            logger.info(f"年龄段数量: {len(results['credit_balance_by_age'])}")
            
            logger.info(f"✅ {test_case['name']} 全部测试通过")
            return results
            
        except Exception as e:
            logger.error(f"❌ {test_case['name']} 测试失败: {str(e)}", exc_info=True)
            raise
    
    async def run_all_tests(self):
        """运行所有测试案例"""
        logger.info("开始执行积分相关函数测试")
        logger.info(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        try:
            # 连接数据库
            await db.connect()
            logger.info("数据库连接成功")
            
            all_results = {}
            
            # 执行所有测试案例
            for test_case in self.test_cases:
                try:
                    results = await self.run_single_test_case(test_case)
                    all_results[test_case['name']] = results
                except Exception as e:
                    logger.error(f"测试案例 {test_case['name']} 执行失败: {str(e)}")
                    all_results[test_case['name']] = {"error": str(e)}
            
            # 输出最终汇总
            logger.info(f"\n{'='*80}")
            logger.info("所有测试案例执行完成 - 最终汇总")
            logger.info(f"{'='*80}")
            
            for case_name, results in all_results.items():
                logger.info(f"\n{case_name}:")
                if "error" in results:
                    logger.error(f"  ❌ 执行失败: {results['error']}")
                else:
                    logger.info(f"  ✅ 执行成功")
                    logger.info(f"  赠送积分总数: {results.get('total_credit_reward', 'N/A')}")
                    logger.info(f"  使用积分总数: {results.get('total_credit_consume', 'N/A')}")
                    logger.info(f"  积分余额分布区间数: {len(results.get('credit_balance_distribution', []))}")
                    logger.info(f"  卡等级数量: {len(results.get('credit_balance_by_level', []))}")
                    logger.info(f"  年龄段数量: {len(results.get('credit_balance_by_age', []))}")
            
            logger.info("\n🎉 积分相关函数测试全部完成!")
            
        except Exception as e:
            logger.error(f"测试执行过程中发生错误: {str(e)}", exc_info=True)
            raise
        finally:
            # 关闭数据库连接
            await db.disconnect()
            logger.info("数据库连接已关闭")

async def main():
    """主函数"""
    test_runner = CreditTestRunner()
    await test_runner.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
