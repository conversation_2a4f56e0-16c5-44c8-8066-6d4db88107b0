import aiomysql
import asyncpg
import logging
from typing import Optional, Dict, Any, List
from contextlib import asynccontextmanager
from core.config import settings

logger = logging.getLogger(__name__)

class Database:
    """数据库连接管理类 - 支持多数据库独立连接"""

    def __init__(self):
        self.dwoutput_pool: Optional[aiomysql.Pool] = None
        self.wedatas_pool: Optional[aiomysql.Pool] = None
        self.welife_hydb_pool: Optional[aiomysql.Pool] = None
        self.pos_dw_pool: Optional[asyncpg.Pool] = None
        self.basic_info_pool: Optional[aiomysql.Pool] = None
        self.backend_pool: Optional[aiomysql.Pool] = None
        
    async def connect(self):
        """创建数据库连接池"""
        try:
            # 获取数据库连接池配置
            pool_config = settings.get_db_pool_config()
            
            # 创建dwoutput数据库连接池
            logger.info(f"正在创建dwoutput数据库连接... [{settings.DWOUTPUT_DB_DESCRIPTION}]")
            self.dwoutput_pool = await aiomysql.create_pool(
                host=settings.DWOUTPUT_DB_HOST,
                port=settings.DWOUTPUT_DB_PORT,
                user=settings.DWOUTPUT_DB_USER,
                password=settings.DWOUTPUT_DB_PASSWORD,
                db=settings.DWOUTPUT_DB_NAME,
                charset=settings.DWOUTPUT_DB_CHARSET,
                minsize=pool_config["minsize"],
                maxsize=pool_config["maxsize"],
                connect_timeout=pool_config["connect_timeout"],
                autocommit=pool_config["autocommit"],
                echo=pool_config["echo"],
            )
            logger.info(f"dwoutput数据库连接池创建成功: {settings.DWOUTPUT_DB_HOST}:{settings.DWOUTPUT_DB_PORT}/{settings.DWOUTPUT_DB_NAME}")
            
            # 创建wedatas数据库连接池
            logger.info(f"正在创建wedatas数据库连接... [{settings.WEDATAS_DB_DESCRIPTION}]")
            self.wedatas_pool = await aiomysql.create_pool(
                host=settings.WEDATAS_DB_HOST,
                port=settings.WEDATAS_DB_PORT,
                user=settings.WEDATAS_DB_USER,
                password=settings.WEDATAS_DB_PASSWORD,
                db=settings.WEDATAS_DB_NAME,
                charset=settings.WEDATAS_DB_CHARSET,
                minsize=pool_config["minsize"],
                maxsize=pool_config["maxsize"],
                connect_timeout=pool_config["connect_timeout"],
                autocommit=pool_config["autocommit"],
                echo=pool_config["echo"],
            )
            logger.info(f"wedatas数据库连接池创建成功: {settings.WEDATAS_DB_HOST}:{settings.WEDATAS_DB_PORT}/{settings.WEDATAS_DB_NAME}")

            # 创建welife_hydb数据库连接池
            logger.info(f"正在创建welife_hydb数据库连接... [{settings.WELIFE_HYDB_DB_DESCRIPTION}]")
            self.welife_hydb_pool = await aiomysql.create_pool(
                host=settings.WELIFE_HYDB_DB_HOST,
                port=settings.WELIFE_HYDB_DB_PORT,
                user=settings.WELIFE_HYDB_DB_USER,
                password=settings.WELIFE_HYDB_DB_PASSWORD,
                db=settings.WELIFE_HYDB_DB_NAME,
                charset=settings.WELIFE_HYDB_DB_CHARSET,
                minsize=pool_config["minsize"],
                maxsize=pool_config["maxsize"],
                connect_timeout=pool_config["connect_timeout"],
                autocommit=pool_config["autocommit"],
                echo=pool_config["echo"],
            )
            logger.info(f"welife_hydb数据库连接池创建成功: {settings.WELIFE_HYDB_DB_HOST}:{settings.WELIFE_HYDB_DB_PORT}/{settings.WELIFE_HYDB_DB_NAME}")

            # 创建basic_info数据库连接池
            logger.info(f"正在创建basic_info数据库连接... [{settings.BASIC_INFO_DB_DESCRIPTION}]")
            self.basic_info_pool = await aiomysql.create_pool(
                host=settings.BASIC_INFO_DB_HOST,
                port=settings.BASIC_INFO_DB_PORT,
                user=settings.BASIC_INFO_DB_USER,
                password=settings.BASIC_INFO_DB_PASSWORD,
                db=settings.BASIC_INFO_DB_NAME,
                charset=settings.BASIC_INFO_DB_CHARSET,
                minsize=pool_config["minsize"],
                maxsize=pool_config["maxsize"],
                connect_timeout=pool_config["connect_timeout"],
                autocommit=pool_config["autocommit"],
                echo=pool_config["echo"],
            )
            logger.info(f"basic_info数据库连接池创建成功: {settings.BASIC_INFO_DB_HOST}:{settings.BASIC_INFO_DB_PORT}/{settings.BASIC_INFO_DB_NAME}")

            # 创建backend数据库连接池
            logger.info(f"正在创建backend数据库连接... [{settings.BACKEND_DB_DESCRIPTION}]")
            self.backend_pool = await aiomysql.create_pool(
                host=settings.BACKEND_DB_HOST,
                port=settings.BACKEND_DB_PORT,
                user=settings.BACKEND_DB_USER,
                password=settings.BACKEND_DB_PASSWORD,
                db=settings.BACKEND_DB_NAME,
                charset=settings.BACKEND_DB_CHARSET,
                minsize=pool_config["minsize"],
                maxsize=pool_config["maxsize"],
                connect_timeout=pool_config["connect_timeout"],
                autocommit=pool_config["autocommit"],
                echo=pool_config["echo"],
            )
            logger.info(f"backend数据库连接池创建成功: {settings.BACKEND_DB_HOST}:{settings.BACKEND_DB_PORT}/{settings.BACKEND_DB_NAME}")

            # 创建品质收银PostgreSQL数据库连接池
            logger.info(f"正在创建品质收银数据库连接... [{settings.POS_DW_DB_DESCRIPTION}]")
            self.pos_dw_pool = await asyncpg.create_pool(
                host=settings.POS_DW_DB_HOST,
                port=settings.POS_DW_DB_PORT,
                user=settings.POS_DW_DB_USER,
                password=settings.POS_DW_DB_PASSWORD,
                database=settings.POS_DW_DB_NAME,
                min_size=pool_config["minsize"],
                max_size=pool_config["maxsize"],
                command_timeout=pool_config["connect_timeout"],
            )
            logger.info(f"品质收银数据库连接池创建成功: {settings.POS_DW_DB_HOST}:{settings.POS_DW_DB_PORT}/{settings.POS_DW_DB_NAME}")

            # 测试连接到所有数据库
            await self._test_database_connections()
            
        except Exception as e:
            logger.error(f"数据库连接失败: {str(e)}")
            raise
    
    async def _test_database_connections(self):
        """测试连接到所有数据库"""
        try:
            # 测试dwoutput数据库
            async with self.get_dwoutput_connection() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute("SELECT 1 AS test_result, CONNECTION_ID() AS connection_id, DATABASE() AS current_db")
                    result = await cursor.fetchone()
                    logger.info(f"dwoutput数据库连接测试成功: {result}")

            # 测试wedatas数据库
            async with self.get_wedatas_connection() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute("SELECT 1 AS test_result, CONNECTION_ID() AS connection_id, DATABASE() AS current_db")
                    result = await cursor.fetchone()
                    logger.info(f"wedatas数据库连接测试成功: {result}")

            # 测试welife_hydb数据库（使用兼容性更好的测试SQL）
            try:
                async with self.get_welife_hydb_connection() as conn:
                    async with conn.cursor() as cursor:
                        # 首先尝试简单的连接测试
                        await cursor.execute("SELECT 1 AS test_result")
                        result = await cursor.fetchone()
                        logger.info(f"welife_hydb数据库连接测试成功: {result}")

                        # 尝试获取数据库信息（如果支持的话）
                        try:
                            await cursor.execute("SELECT DATABASE() AS current_db")
                            db_result = await cursor.fetchone()
                            logger.info(f"welife_hydb当前数据库: {db_result}")
                        except Exception as db_info_error:
                            logger.warning(f"welife_hydb数据库信息获取失败（这是正常的，某些云数据库不支持此功能）: {db_info_error}")
            except Exception as welife_error:
                logger.error(f"welife_hydb数据库连接测试失败: {welife_error}")
                logger.warning("welife_hydb数据库连接失败，但不影响其他数据库的使用")
                # 不抛出异常，允许应用继续启动

            # 测试basic_info数据库
            async with self.get_basic_info_connection() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute("SELECT 1 AS test_result, CONNECTION_ID() AS connection_id, DATABASE() AS current_db")
                    result = await cursor.fetchone()
                    logger.info(f"basic_info数据库连接测试成功: {result}")

            # 测试backend数据库
            async with self.get_backend_connection() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute("SELECT 1 AS test_result, CONNECTION_ID() AS connection_id, DATABASE() AS current_db")
                    result = await cursor.fetchone()
                    logger.info(f"backend数据库连接测试成功: {result}")

            # 测试品质收银PostgreSQL数据库
            async with self.get_pos_dw_connection() as conn:
                result = await conn.fetchrow("SELECT 1 AS test_result, current_database() AS current_db, version() AS pg_version")
                logger.info(f"品质收银数据库连接测试成功: {dict(result)}")
                    
        except Exception as e:
            logger.error(f"关键数据库连接测试失败: {str(e)}")
            logger.error("请检查：")
            logger.error("1. dwoutput和wedatas数据库服务器可访问")
            logger.error("2. 数据库名称和用户权限配置正确")
            logger.error("3. 网络连接正常")
            logger.error("4. 数据库服务正在运行")
            logger.info("注意：welife_hydb数据库连接失败不会阻止应用启动")
            raise
    
    async def disconnect(self):
        """关闭数据库连接池"""
        if self.dwoutput_pool:
            self.dwoutput_pool.close()
            await self.dwoutput_pool.wait_closed()
            logger.info("dwoutput数据库连接池已关闭")

        if self.wedatas_pool:
            self.wedatas_pool.close()
            await self.wedatas_pool.wait_closed()
            logger.info("wedatas数据库连接池已关闭")

        if self.welife_hydb_pool:
            self.welife_hydb_pool.close()
            await self.welife_hydb_pool.wait_closed()
            logger.info("welife_hydb数据库连接池已关闭")

        if self.basic_info_pool:
            self.basic_info_pool.close()
            await self.basic_info_pool.wait_closed()
            logger.info("basic_info数据库连接池已关闭")

        if self.backend_pool:
            self.backend_pool.close()
            await self.backend_pool.wait_closed()
            logger.info("backend数据库连接池已关闭")

        if self.pos_dw_pool:
            await self.pos_dw_pool.close()
            logger.info("品质收银数据库连接池已关闭")
    
    @asynccontextmanager
    async def get_dwoutput_connection(self):
        """获取dwoutput数据库连接的上下文管理器"""
        if not self.dwoutput_pool:
            await self.connect()
        
        async with self.dwoutput_pool.acquire() as conn:
            try:
                yield conn
            except Exception as e:
                logger.error(f"dwoutput数据库操作异常: {str(e)}")
                raise
    
    @asynccontextmanager
    async def get_wedatas_connection(self):
        """获取wedatas数据库连接的上下文管理器"""
        if not self.wedatas_pool:
            await self.connect()

        async with self.wedatas_pool.acquire() as conn:
            try:
                yield conn
            except Exception as e:
                logger.error(f"wedatas数据库操作异常: {str(e)}")
                raise

    @asynccontextmanager
    async def get_welife_hydb_connection(self):
        """获取welife_hydb数据库连接的上下文管理器"""
        if not self.welife_hydb_pool:
            await self.connect()

        async with self.welife_hydb_pool.acquire() as conn:
            try:
                yield conn
            except Exception as e:
                logger.error(f"welife_hydb数据库操作异常: {str(e)}")
                raise

    @asynccontextmanager
    async def get_basic_info_connection(self):
        """获取basic_info数据库连接的上下文管理器"""
        if not self.basic_info_pool:
            await self.connect()

        async with self.basic_info_pool.acquire() as conn:
            try:
                yield conn
            except Exception as e:
                logger.error(f"basic_info数据库操作异常: {str(e)}")
                raise

    @asynccontextmanager
    async def get_backend_connection(self):
        """获取backend数据库连接的上下文管理器"""
        if not self.backend_pool:
            await self.connect()

        async with self.backend_pool.acquire() as conn:
            try:
                yield conn
            except Exception as e:
                logger.error(f"backend数据库操作异常: {str(e)}")
                raise

    @asynccontextmanager
    async def get_pos_dw_connection(self):
        """获取品质收银PostgreSQL数据库连接的上下文管理器"""
        if not self.pos_dw_pool:
            await self.connect()

        async with self.pos_dw_pool.acquire() as conn:
            try:
                yield conn
            except Exception as e:
                logger.error(f"品质收银数据库操作异常: {str(e)}")
                raise

    # 兼容性方法：保持原有的API
    @asynccontextmanager
    async def get_connection(self):
        """获取数据库连接的上下文管理器 - 默认使用dwoutput"""
        async with self.get_dwoutput_connection() as conn:
            yield conn
    
    async def execute_dwoutput_query(self, sql: str, params: tuple = None) -> List[Dict[str, Any]]:
        """执行dwoutput数据库查询SQL"""
        import time
        start_time = time.time()
        
        async with self.get_dwoutput_connection() as conn:
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute(sql, params)
                result = await cursor.fetchall()
                
                query_time = time.time() - start_time
                logger.info(f"dwoutput查询完成，返回{len(result)}条记录，耗时{query_time:.3f}秒")
                
                # 记录慢查询
                if settings.DB_QUERY_LOG_ENABLED and query_time > settings.DB_QUERY_SLOW_THRESHOLD:
                    logger.warning(f"dwoutput慢查询检测: {query_time:.3f}秒 - SQL: {sql[:200]}...")
                
                logger.debug(f"dwoutput查询SQL: {sql[:200]}...")
                return result
    
    async def execute_dwoutput_one(self, sql: str, params: tuple = None) -> Optional[Dict[str, Any]]:
        """执行dwoutput数据库查询SQL并返回单条记录"""
        import time
        start_time = time.time()
        
        async with self.get_dwoutput_connection() as conn:
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute(sql, params)
                result = await cursor.fetchone()
                
                query_time = time.time() - start_time
                logger.info(f"dwoutput单条查询完成，结果: {'有数据' if result else '无数据'}，耗时{query_time:.3f}秒")
                
                # 记录慢查询
                if settings.DB_QUERY_LOG_ENABLED and query_time > settings.DB_QUERY_SLOW_THRESHOLD:
                    logger.warning(f"dwoutput慢查询检测: {query_time:.3f}秒 - SQL: {sql[:200]}...")
                
                logger.debug(f"dwoutput单条查询SQL: {sql[:200]}...")
                return result
    
    async def execute_wedatas_query(self, sql: str, params: tuple = None) -> List[Dict[str, Any]]:
        """执行wedatas数据库查询SQL"""
        import time
        start_time = time.time()
        
        async with self.get_wedatas_connection() as conn:
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute(sql, params)
                result = await cursor.fetchall()
                
                query_time = time.time() - start_time
                logger.info(f"wedatas查询完成，返回{len(result)}条记录，耗时{query_time:.3f}秒")
                
                # 记录慢查询
                if settings.DB_QUERY_LOG_ENABLED and query_time > settings.DB_QUERY_SLOW_THRESHOLD:
                    logger.warning(f"wedatas慢查询检测: {query_time:.3f}秒 - SQL: {sql[:200]}...")
                
                logger.debug(f"wedatas查询SQL: {sql[:200]}...")
                return result
    
    async def execute_wedatas_one(self, sql: str, params: tuple = None) -> Optional[Dict[str, Any]]:
        """执行wedatas数据库查询SQL并返回单条记录"""
        import time
        start_time = time.time()
        
        async with self.get_wedatas_connection() as conn:
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute(sql, params)
                result = await cursor.fetchone()
                
                query_time = time.time() - start_time
                logger.info(f"wedatas单条查询完成，结果: {'有数据' if result else '无数据'}，耗时{query_time:.3f}秒")
                
                # 记录慢查询
                if settings.DB_QUERY_LOG_ENABLED and query_time > settings.DB_QUERY_SLOW_THRESHOLD:
                    logger.warning(f"wedatas慢查询检测: {query_time:.3f}秒 - SQL: {sql[:200]}...")
                
                logger.debug(f"wedatas单条查询SQL: {sql[:200]}...")
                return result

    async def execute_welife_hydb_query(self, sql: str, params: tuple = None) -> List[Dict[str, Any]]:
        """执行welife_hydb数据库查询SQL"""
        import time
        start_time = time.time()

        try:
            async with self.get_welife_hydb_connection() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    await cursor.execute(sql, params)
                    result = await cursor.fetchall()

                    query_time = time.time() - start_time
                    logger.info(f"welife_hydb查询完成，返回{len(result)}条记录，耗时{query_time:.3f}秒")

                    # 记录慢查询
                    if settings.DB_QUERY_LOG_ENABLED and query_time > settings.DB_QUERY_SLOW_THRESHOLD:
                        logger.warning(f"welife_hydb慢查询检测: {query_time:.3f}秒 - SQL: {sql[:200]}...")

                    logger.debug(f"welife_hydb查询SQL: {sql[:200]}...")
                    return result
        except Exception as e:
            logger.error(f"welife_hydb数据库查询失败: {e}")
            logger.error(f"SQL: {sql[:200]}...")
            return []  # 返回空列表而不是抛出异常

    async def execute_welife_hydb_one(self, sql: str, params: tuple = None) -> Optional[Dict[str, Any]]:
        """执行welife_hydb数据库查询SQL并返回单条记录"""
        import time
        start_time = time.time()

        try:
            async with self.get_welife_hydb_connection() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    await cursor.execute(sql, params)
                    result = await cursor.fetchone()

                    query_time = time.time() - start_time
                    logger.info(f"welife_hydb单条查询完成，结果: {'有数据' if result else '无数据'}，耗时{query_time:.3f}秒")

                    # 记录慢查询
                    if settings.DB_QUERY_LOG_ENABLED and query_time > settings.DB_QUERY_SLOW_THRESHOLD:
                        logger.warning(f"welife_hydb慢查询检测: {query_time:.3f}秒 - SQL: {sql[:200]}...")

                    logger.debug(f"welife_hydb单条查询SQL: {sql[:200]}...")
                    return result
        except Exception as e:
            logger.error(f"welife_hydb数据库单条查询失败: {e}")
            logger.error(f"SQL: {sql[:200]}...")
            return None  # 返回None而不是抛出异常

    async def execute_basic_info_query(self, sql: str, params: tuple = None) -> List[Dict[str, Any]]:
        """执行basic_info数据库查询SQL"""
        import time
        start_time = time.time()

        async with self.get_basic_info_connection() as conn:
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute(sql, params)
                result = await cursor.fetchall()

                query_time = time.time() - start_time
                logger.info(f"basic_info查询完成，返回{len(result)}条记录，耗时{query_time:.3f}秒")

                # 记录慢查询
                if settings.DB_QUERY_LOG_ENABLED and query_time > settings.DB_QUERY_SLOW_THRESHOLD:
                    logger.warning(f"basic_info慢查询检测: {query_time:.3f}秒 - SQL: {sql[:200]}...")

                logger.debug(f"basic_info查询SQL: {sql[:200]}...")
                return result

    async def execute_basic_info_one(self, sql: str, params: tuple = None) -> Optional[Dict[str, Any]]:
        """执行basic_info数据库查询SQL并返回单条记录"""
        import time
        start_time = time.time()

        async with self.get_basic_info_connection() as conn:
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute(sql, params)
                result = await cursor.fetchone()

                query_time = time.time() - start_time
                logger.info(f"basic_info单条查询完成，结果: {'有数据' if result else '无数据'}，耗时{query_time:.3f}秒")

                # 记录慢查询
                if settings.DB_QUERY_LOG_ENABLED and query_time > settings.DB_QUERY_SLOW_THRESHOLD:
                    logger.warning(f"basic_info慢查询检测: {query_time:.3f}秒 - SQL: {sql[:200]}...")

                logger.debug(f"basic_info单条查询SQL: {sql[:200]}...")
                return result

    async def execute_backend_query(self, sql: str, params: tuple = None) -> List[Dict[str, Any]]:
        """执行backend数据库查询SQL"""
        import time
        start_time = time.time()

        async with self.get_backend_connection() as conn:
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute(sql, params)
                result = await cursor.fetchall()

                query_time = time.time() - start_time
                logger.info(f"backend查询完成，返回{len(result)}条记录，耗时{query_time:.3f}秒")

                # 记录慢查询
                if settings.DB_QUERY_LOG_ENABLED and query_time > settings.DB_QUERY_SLOW_THRESHOLD:
                    logger.warning(f"backend慢查询检测: {query_time:.3f}秒 - SQL: {sql[:200]}...")

                logger.debug(f"backend查询SQL: {sql[:200]}...")
                return result

    async def execute_backend_one(self, sql: str, params: tuple = None) -> Optional[Dict[str, Any]]:
        """执行backend数据库查询SQL并返回单条记录"""
        import time
        start_time = time.time()

        async with self.get_backend_connection() as conn:
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute(sql, params)
                result = await cursor.fetchone()

                query_time = time.time() - start_time
                logger.info(f"backend单条查询完成，结果: {'有数据' if result else '无数据'}，耗时{query_time:.3f}秒")

                # 记录慢查询
                if settings.DB_QUERY_LOG_ENABLED and query_time > settings.DB_QUERY_SLOW_THRESHOLD:
                    logger.warning(f"backend慢查询检测: {query_time:.3f}秒 - SQL: {sql[:200]}...")

                logger.debug(f"backend单条查询SQL: {sql[:200]}...")
                return result

    async def execute_pos_dw_query(self, sql: str, params: tuple = None) -> List[Dict[str, Any]]:
        """执行品质收银数据库查询SQL并返回多条记录"""
        import time
        start_time = time.time()

        try:
            async with self.get_pos_dw_connection() as conn:
                logger.info(f"品智收银数据库连接成功，准备执行SQL")
                logger.info(f"执行的SQL: {sql}")

                # PostgreSQL使用asyncpg，参数格式不同
                if params:
                    rows = await conn.fetch(sql, *params)
                else:
                    rows = await conn.fetch(sql)

                logger.info(f"SQL执行成功，获得{len(rows)}行原始数据")

                # 将asyncpg的Record对象转换为字典
                result = [dict(row) for row in rows]

                # 详细记录查询结果
                if result:
                    logger.info(f"第一行数据详情: {result[0]}")
                    for key, value in result[0].items():
                        logger.info(f"字段 '{key}': {value} (类型: {type(value)})")

                query_time = time.time() - start_time
                logger.info(f"品质收银查询完成，结果: {len(result)}条记录，耗时{query_time:.3f}秒")

                # 记录慢查询
                if settings.DB_QUERY_LOG_ENABLED and query_time > settings.DB_QUERY_SLOW_THRESHOLD:
                    logger.warning(f"品质收银慢查询检测: {query_time:.3f}秒 - SQL: {sql[:200]}...")

                logger.debug(f"品质收银查询SQL: {sql[:200]}...")
                return result
        except Exception as e:
            logger.error(f"=== 品智收银数据库异常详情 ===")
            logger.error(f"异常类型: {type(e)}")
            logger.error(f"异常消息: {str(e)}")
            logger.error(f"执行的SQL: {sql}")
            logger.error(f"SQL参数: {params}")
            logger.error(f"异常堆栈:", exc_info=True)
            logger.error(f"=== 品智收银数据库异常结束 ===")

            # 返回空结果，让上层知道数据库连接失败
            return []

    async def execute_pos_dw_one(self, sql: str, params: tuple = None) -> Optional[Dict[str, Any]]:
        """执行品质收银数据库查询SQL并返回单条记录"""
        import time
        start_time = time.time()

        async with self.get_pos_dw_connection() as conn:
            # PostgreSQL使用asyncpg，参数格式不同
            if params:
                row = await conn.fetchrow(sql, *params)
            else:
                row = await conn.fetchrow(sql)

            result = dict(row) if row else None

            query_time = time.time() - start_time
            logger.info(f"品质收银单条查询完成，结果: {'有数据' if result else '无数据'}，耗时{query_time:.3f}秒")

            # 记录慢查询
            if settings.DB_QUERY_LOG_ENABLED and query_time > settings.DB_QUERY_SLOW_THRESHOLD:
                logger.warning(f"品质收银慢查询检测: {query_time:.3f}秒 - SQL: {sql[:200]}...")

            logger.debug(f"品质收银单条查询SQL: {sql[:200]}...")
            return result

    # 兼容性方法：保持原有的API
    async def execute_query(self, sql: str, params: tuple = None) -> List[Dict[str, Any]]:
        """执行查询SQL - 默认使用dwoutput数据库"""
        return await self.execute_dwoutput_query(sql, params)
    
    async def execute_one(self, sql: str, params: tuple = None) -> Optional[Dict[str, Any]]:
        """执行查询SQL并返回单条记录 - 默认使用dwoutput数据库"""
        return await self.execute_dwoutput_one(sql, params)
    
    async def execute_update(self, sql: str, params: tuple = None) -> int:
        """执行更新SQL - 默认使用dwoutput数据库"""
        import time
        start_time = time.time()
        
        async with self.get_dwoutput_connection() as conn:
            async with conn.cursor() as cursor:
                result = await cursor.execute(sql, params)
                
                query_time = time.time() - start_time
                logger.info(f"dwoutput更新操作完成，影响{result}行，耗时{query_time:.3f}秒")
                
                # 记录慢查询
                if settings.DB_QUERY_LOG_ENABLED and query_time > settings.DB_QUERY_SLOW_THRESHOLD:
                    logger.warning(f"dwoutput慢更新检测: {query_time:.3f}秒 - SQL: {sql[:200]}...")
                
                logger.debug(f"dwoutput更新SQL: {sql[:200]}...")
                return result
    
    async def get_connection_status(self) -> Dict[str, Any]:
        """获取数据库连接状态"""
        status = {
            "dwoutput": {
                "connected": self.dwoutput_pool is not None,
                "pool_size": self.dwoutput_pool.size if self.dwoutput_pool else 0,
                "free_size": self.dwoutput_pool.freesize if self.dwoutput_pool else 0,
            },
            "wedatas": {
                "connected": self.wedatas_pool is not None,
                "pool_size": self.wedatas_pool.size if self.wedatas_pool else 0,
                "free_size": self.wedatas_pool.freesize if self.wedatas_pool else 0,
            },
            "welife_hydb": {
                "connected": self.welife_hydb_pool is not None,
                "pool_size": self.welife_hydb_pool.size if self.welife_hydb_pool else 0,
                "free_size": self.welife_hydb_pool.freesize if self.welife_hydb_pool else 0,
            },
            "basic_info": {
                "connected": self.basic_info_pool is not None,
                "pool_size": self.basic_info_pool.size if self.basic_info_pool else 0,
                "free_size": self.basic_info_pool.freesize if self.basic_info_pool else 0,
            },
            "backend": {
                "connected": self.backend_pool is not None,
                "pool_size": self.backend_pool.size if self.backend_pool else 0,
                "free_size": self.backend_pool.freesize if self.backend_pool else 0,
            }
        }
        return status

# 创建数据库实例
db = Database()