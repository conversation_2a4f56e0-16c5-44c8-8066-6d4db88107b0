import logging
from typing import Optional
from core.database import db

logger = logging.getLogger(__name__)

class FirstChargeSqlQueries:
    """首充和次充相关SQL查询类"""

    @staticmethod
    async def get_first_charge_user_count(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> int:
        """获取首充人数统计

        数据库：dwoutput.dprpt_welife_charge_detail
        字段：ucid（用户ID）, isFirst（是否首充：0=首充，>0=次充）
        计算方式：统计isFirst=0的不重复用户数量

        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)

        Returns:
            首充人数
        """
        try:
            sid_condition = f"AND a.sid = '{sid}'" if sid else ""

            sql = f"""
            SELECT COUNT(DISTINCT CASE WHEN isFirst = 0 THEN ucid END) AS first_time_user_count
            FROM dwoutput.dprpt_welife_charge_detail a
            WHERE a.bid = {bid}
              AND a.tcType = 1
              AND a.ftime BETWEEN {start_date} AND {end_date}
              {sid_condition}
            """

            logger.info(f"执行首充人数查询 - bid: {bid}, sid: {sid}, 时间范围: {start_date}-{end_date}")
            logger.debug(f"首充人数查询SQL: {sql}")

            result = await db.execute_dwoutput_one(sql)
            count = result.get('first_time_user_count', 0) if result else 0

            logger.info(f"首充人数查询完成，结果: {count}")
            return count

        except Exception as e:
            logger.error(f"获取首充人数失败: {str(e)}", exc_info=True)
            raise

    @staticmethod
    async def get_repeat_charge_user_count(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> int:
        """获取次充人数统计

        数据库：dwoutput.dprpt_welife_charge_detail
        字段：ucid（用户ID）, isFirst（是否首充：0=首充，>0=次充）
        计算方式：统计isFirst>0的不重复用户数量

        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)

        Returns:
            次充人数
        """
        try:
            sid_condition = f"AND a.sid = '{sid}'" if sid else ""

            sql = f"""
            SELECT COUNT(DISTINCT CASE WHEN isFirst > 0 THEN ucid END) AS repeat_user_count
            FROM dwoutput.dprpt_welife_charge_detail a
            WHERE a.bid = {bid}
              AND a.tcType = 1
              AND a.ftime BETWEEN {start_date} AND {end_date}
              {sid_condition}
            """

            logger.info(f"执行次充人数查询 - bid: {bid}, sid: {sid}, 时间范围: {start_date}-{end_date}")
            logger.debug(f"次充人数查询SQL: {sql}")

            result = await db.execute_dwoutput_one(sql)
            count = result.get('repeat_user_count', 0) if result else 0

            logger.info(f"次充人数查询完成，结果: {count}")
            return count

        except Exception as e:
            logger.error(f"获取次充人数失败: {str(e)}", exc_info=True)
            raise

    @staticmethod
    async def get_first_charge_count(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> int:
        """获取首充次数统计

        数据库：dwoutput.dprpt_welife_charge_detail
        字段：isFirst（是否首充：0=首充，>0=次充）
        计算方式：统计isFirst=0的记录总数

        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)

        Returns:
            首充次数
        """
        try:
            sid_condition = f"AND a.sid = '{sid}'" if sid else ""

            sql = f"""
            SELECT COUNT(CASE WHEN isFirst = 0 THEN 1 END) AS first_time_user_charge_count
            FROM dwoutput.dprpt_welife_charge_detail a
            WHERE a.bid = {bid}
              AND a.tcType = 1
              AND a.ftime BETWEEN {start_date} AND {end_date}
              {sid_condition}
            """

            logger.info(f"执行首充次数查询 - bid: {bid}, sid: {sid}, 时间范围: {start_date}-{end_date}")
            logger.debug(f"首充次数查询SQL: {sql}")

            result = await db.execute_dwoutput_one(sql)
            count = result.get('first_time_user_charge_count', 0) if result else 0

            logger.info(f"首充次数查询完成，结果: {count}")
            return count

        except Exception as e:
            logger.error(f"获取首充次数失败: {str(e)}", exc_info=True)
            raise

    @staticmethod
    async def get_repeat_charge_count(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> int:
        """获取次充次数统计

        数据库：dwoutput.dprpt_welife_charge_detail
        字段：isFirst（是否首充：0=首充，>0=次充）
        计算方式：统计isFirst>0的记录总数

        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)

        Returns:
            次充次数
        """
        try:
            sid_condition = f"AND a.sid = '{sid}'" if sid else ""

            sql = f"""
            SELECT COUNT(CASE WHEN isFirst > 0 THEN 1 END) AS repeat_user_charge_count
            FROM dwoutput.dprpt_welife_charge_detail a
            WHERE a.bid = {bid}
              AND a.tcType = 1
              AND a.ftime BETWEEN {start_date} AND {end_date}
              {sid_condition}
            """

            logger.info(f"执行次充次数查询 - bid: {bid}, sid: {sid}, 时间范围: {start_date}-{end_date}")
            logger.debug(f"次充次数查询SQL: {sql}")

            result = await db.execute_dwoutput_one(sql)
            count = result.get('repeat_user_charge_count', 0) if result else 0

            logger.info(f"次充次数查询完成，结果: {count}")
            return count

        except Exception as e:
            logger.error(f"获取次充次数失败: {str(e)}", exc_info=True)
            raise

    @staticmethod
    async def get_first_charge_amount(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> float:
        """获取首充金额统计

        数据库：dwoutput.dprpt_welife_charge_detail
        字段：charge_cash（充值金额）, isFirst（是否首充：0=首充，>0=次充）
        计算方式：统计isFirst=0的记录的charge_cash总和

        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)

        Returns:
            首充金额
        """
        try:
            sid_condition = f"AND a.sid = '{sid}'" if sid else ""

            sql = f"""
            SELECT SUM(CASE WHEN isFirst = 0 THEN charge_cash ELSE 0 END) AS first_time_user_charge_amount
            FROM dwoutput.dprpt_welife_charge_detail a
            WHERE a.bid = {bid}
              AND a.tcType = 1
              AND a.ftime BETWEEN {start_date} AND {end_date}
              {sid_condition}
            """

            logger.info(f"执行首充金额查询 - bid: {bid}, sid: {sid}, 时间范围: {start_date}-{end_date}")
            logger.debug(f"首充金额查询SQL: {sql}")

            result = await db.execute_dwoutput_one(sql)
            amount = result.get('first_time_user_charge_amount', 0) if result else 0
            # 处理None值
            amount = float(amount) if amount is not None else 0.0

            logger.info(f"首充金额查询完成，结果: {amount}")
            return amount

        except Exception as e:
            logger.error(f"获取首充金额失败: {str(e)}", exc_info=True)
            raise

    @staticmethod
    async def get_repeat_charge_amount(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> float:
        """获取次充金额统计

        数据库：dwoutput.dprpt_welife_charge_detail
        字段：charge_cash（充值金额）, isFirst（是否首充：0=首充，>0=次充）
        计算方式：统计isFirst>0的记录的charge_cash总和

        Args:
            start_date: 开始日期 (YYYYMMDD格式)
            end_date: 结束日期 (YYYYMMDD格式)
            bid: 品牌ID
            sid: 门店ID (可选)

        Returns:
            次充金额
        """
        try:
            sid_condition = f"AND a.sid = '{sid}'" if sid else ""

            sql = f"""
            SELECT SUM(CASE WHEN isFirst > 0 THEN charge_cash ELSE 0 END) AS repeat_user_charge_amount
            FROM dwoutput.dprpt_welife_charge_detail a
            WHERE a.bid = {bid}
              AND a.tcType = 1
              AND a.ftime BETWEEN {start_date} AND {end_date}
              {sid_condition}
            """

            logger.info(f"执行次充金额查询 - bid: {bid}, sid: {sid}, 时间范围: {start_date}-{end_date}")
            logger.debug(f"次充金额查询SQL: {sql}")

            result = await db.execute_dwoutput_one(sql)
            amount = result.get('repeat_user_charge_amount', 0) if result else 0
            # 处理None值
            amount = float(amount) if amount is not None else 0.0

            logger.info(f"次充金额查询完成，结果: {amount}")
            return amount

        except Exception as e:
            logger.error(f"获取次充金额失败: {str(e)}", exc_info=True)
            raise