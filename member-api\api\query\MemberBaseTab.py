from fastapi import APIRouter, HTTPException, Depends
from typing import Optional, Dict, Any, List
import logging
from datetime import datetime, timedelta
import asyncio
import time

from core.database import db
from core.models import QueryParams, MemberBaseData, FieldDataModel, ResponseModel
from constant import MEMBER_BASE_MODULE
from api.query.MemberBaseSql import MemberBaseSqlQueries, MemberBaseSqlCalculator
from api.query.MemberBaseSqlAdd import MemberBaseCalculatorAdd

logger = logging.getLogger(__name__)

router = APIRouter()

class MemberBaseService:
    """会员基础数据服务"""
    
    def __init__(self):
        self.module_config = MEMBER_BASE_MODULE
        self.sql_queries = MemberBaseSqlQueries()
        self.calculator = MemberBaseSqlCalculator()
    
    def _format_date_for_sql(self, date_str: str) -> str:
        """将日期字符串格式化为SQL需要的格式 YYYYMMDD"""
        try:
            date_obj = datetime.strptime(date_str, "%Y-%m-%d")
            return date_obj.strftime("%Y%m%d")
        except ValueError:
            raise HTTPException(status_code=400, detail=f"日期格式错误: {date_str}")
    
    def _calculate_time_ranges(self, query_params: QueryParams) -> Dict[str, Any]:
        """计算时间范围，包括当前期间、环比期间、同比期间"""
        start_date = datetime.strptime(query_params.start_date, "%Y-%m-%d")
        end_date = datetime.strptime(query_params.end_date, "%Y-%m-%d")
        
        # 计算期间长度
        period_days = (end_date - start_date).days + 1
        
        # 环比期间（上一个相同长度的期间）
        chain_end_date = start_date - timedelta(days=1)
        chain_start_date = chain_end_date - timedelta(days=period_days - 1)
        
        # 同比期间（去年同期）- 处理闰年问题
        try:
            year_over_year_start = start_date.replace(year=start_date.year - 1)
        except ValueError:
            # 处理2月29日的情况，改为2月28日
            year_over_year_start = start_date.replace(year=start_date.year - 1, day=28)

        try:
            year_over_year_end = end_date.replace(year=end_date.year - 1)
        except ValueError:
            # 处理2月29日的情况，改为2月28日
            year_over_year_end = end_date.replace(year=end_date.year - 1, day=28)
        
        return {
            "current": {
                "start": start_date.strftime("%Y%m%d"),
                "end": end_date.strftime("%Y%m%d"),
                "label": "本期"
            },
            "chain": {
                "start": chain_start_date.strftime("%Y%m%d"),
                "end": chain_end_date.strftime("%Y%m%d"),
                "label": "上期"
            },
            "year_over_year": {
                "start": year_over_year_start.strftime("%Y%m%d"),
                "end": year_over_year_end.strftime("%Y%m%d"),
                "label": "去年同期"
            }
        }
    
    async def _fetch_dwoutput_data_v2(self, start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> Dict[str, Any]:
        """获取dwoutput数据库的会员基础数据 - 独立查询版本"""
        try:
            logger.info(f"开始查询dwoutput数据库 - bid: {bid}, sid: {sid}, 时间范围: {start_date}-{end_date}")
            logger.warning(f"注意：查询日期为 {start_date}-{end_date}，请确认这是正确的日期范围！")
            start_time = time.time()
            
            # 独立执行每个SQL查询
            result = {}
            
            # 1. 查询会员总数量
            total_all_user_sql = f"SELECT {MemberBaseSqlQueries.get_dwoutput_total_all_user_sql(start_date, end_date, bid, sid)}"
            total_all_user_result = await db.execute_dwoutput_one(total_all_user_sql)
            result['total_all_user'] = total_all_user_result.get('total_all_user', 0) if total_all_user_result else 0

            # 2. 查询会员净存量
            net_all_user_sql = f"SELECT {MemberBaseSqlQueries.get_dwoutput_net_all_user_sql(start_date, end_date, bid, sid)}"
            net_all_user_result = await db.execute_dwoutput_one(net_all_user_sql)
            result['net_all_user'] = net_all_user_result.get('net_all_user', 0) if net_all_user_result else 0

            # 3. 查询取关会员数量
            total_cancel_user_sql = f"SELECT {MemberBaseSqlQueries.get_dwoutput_total_cancel_user_sql(start_date, end_date, bid, sid)}"
            total_cancel_user_result = await db.execute_dwoutput_one(total_cancel_user_sql)
            result['total_cancel_user'] = total_cancel_user_result.get('total_cancel_user', 0) if total_cancel_user_result else 0

            # 4. 查询新增会员数量
            new_user_total_sql = f"SELECT {MemberBaseSqlQueries.get_dwoutput_new_user_total_sql(start_date, end_date, bid, sid)}"
            logger.info(f"新增会员查询SQL: {new_user_total_sql}")
            new_user_total_result = await db.execute_dwoutput_one(new_user_total_sql)
            logger.info(f"新增会员查询结果: {new_user_total_result}")
            result['new_user_total'] = new_user_total_result.get('new_user_total', 0) if new_user_total_result else 0

            # 5. 查询新增取消注册会员数量
            new_cancel_user_sql = f"SELECT {MemberBaseSqlQueries.get_dwoutput_new_cancel_user_sql(start_date, end_date, bid, sid)}"
            new_cancel_user_result = await db.execute_dwoutput_one(new_cancel_user_sql)
            result['new_cancel_user'] = new_cancel_user_result.get('new_cancel_user', 0) if new_cancel_user_result else 0

            
            query_time = time.time() - start_time
            logger.info(f"dwoutput数据库查询完成，耗时: {query_time:.3f}秒")
            
            # 计算取关占比（使用新逻辑）
            new_cancel_user = result.get('new_cancel_user', 0) or 0
            new_user_total = result.get('new_user_total', 0) or 0

            # 确保数据类型转换
            new_cancel_user = float(new_cancel_user) if new_cancel_user is not None else 0.0
            new_user_total = float(new_user_total) if new_user_total is not None else 0.0

            # 使用重构后的独立计算函数
            cancel_user_rate = MemberBaseSqlCalculator.calculate_cancel_user_rate(
                new_cancel_user, new_user_total
            )
            result['cancel_user_rate'] = cancel_user_rate
            
            logger.info(f"dwoutput数据处理完成: {len(result)}个字段")
            logger.info(f"最终结果: {result}")
            return result
            
        except Exception as e:
            logger.error(f"获取dwoutput数据失败: {str(e)}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"dwoutput数据查询失败: {str(e)}")
    
    async def _fetch_dwoutput_data(self, start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> Dict[str, Any]:
        """获取dwoutput数据库的会员基础数据 - 独立查询版本"""
        try:
            logger.info(f"开始查询dwoutput数据库 - bid: {bid}, sid: {sid}, 时间范围: {start_date}-{end_date}")
            start_time = time.time()
            
            result = {}
            sid_condition = f"AND sid = '{sid}'" if sid else ""
            
            # 1. 查询累计消费会员
            total_all_user_consomer_sql = f"SELECT {MemberBaseSqlQueries.get_dwoutput_total_all_user_consomer_sql(start_date, end_date, bid, sid)}"
            logger.info(f"累计消费会员查询SQL: {total_all_user_consomer_sql}")
            total_all_user_consomer_result = await db.execute_dwoutput_one(total_all_user_consomer_sql)
            result['total_all_user_consomer'] = total_all_user_consomer_result.get('total_all_user_consomer', 0) if total_all_user_consomer_result else 0
            
            # 2. 查询累计储值会员
            total_all_user_charger_sql = f"SELECT {MemberBaseSqlQueries.get_dwoutput_total_all_user_charger_sql(start_date, end_date, bid, sid)}"
            total_all_user_charger_result = await db.execute_dwoutput_one(total_all_user_charger_sql)
            result['total_all_user_charger'] = total_all_user_charger_result.get('total_all_user_charger', 0) if total_all_user_charger_result else 0
            
            # 3. 查询新增有消费的会员数
            new_user_consomer_total_sql = f"SELECT {MemberBaseSqlQueries.get_dwoutput_new_user_consomer_total_sql(start_date, end_date, bid, sid)}"
            new_user_consomer_total_result = await db.execute_dwoutput_one(new_user_consomer_total_sql)
            result['new_user_consomer_total'] = new_user_consomer_total_result.get('new_user_consomer_total', 0) if new_user_consomer_total_result else 0
            
            # 4. 查询新增有储值的会员数
            new_user_charger_total_sql = f"SELECT {MemberBaseSqlQueries.get_dwoutput_new_user_charger_total_sql(start_date, end_date, bid, sid)}"
            new_user_charger_total_result = await db.execute_dwoutput_one(new_user_charger_total_sql)
            result['new_user_charger_total'] = new_user_charger_total_result.get('new_user_charger_total', 0) if new_user_charger_total_result else 0
            
            # 获取统计数据的最大日期（仅用于某些指标）
            max_date_subquery = MemberBaseSqlQueries.get_dwoutput_max_date_subquery(start_date, end_date, bid, sid, "dprpt_welife_users_stat")
            
            # 5. 查询会员手机号完善数（最后一天）
            phone_sql = f"""
            SELECT {MemberBaseSqlQueries.get_dwoutput_total_card_phone_num_sql(start_date, end_date, bid, sid)}
            """
            phone_result = await db.execute_dwoutput_one(phone_sql)
            result['total_card_phone_num'] = phone_result.get('total_card_phone_num', 0) if phone_result else 0

            # 6. 查询会员信息完善数（最后一天）
            info_sql = f"""
            SELECT {MemberBaseSqlQueries.get_dwoutput_total_card_info_num_sql(start_date, end_date, bid, sid)}
            """
            info_result = await db.execute_dwoutput_one(info_sql)
            result['total_card_info_num'] = info_result.get('total_card_info_num', 0) if info_result else 0
            
            # 7. 查询消费0次的会员数（最后一天）
            consume_0_sql = f"""
            SELECT {MemberBaseSqlQueries.get_dwoutput_total_consume_0_num_sql(start_date, end_date, bid, sid)}
            """
            consume_0_result = await db.execute_dwoutput_one(consume_0_sql)
            result['total_consume_0_num'] = consume_0_result.get('total_consume_0_num', 0) if consume_0_result else 0

            # 8. 查询消费1次的会员数（最后一天）
            consume_1_sql = f"""
            SELECT {MemberBaseSqlQueries.get_dwoutput_total_consume_1_num_sql(start_date, end_date, bid, sid)}
            """
            consume_1_result = await db.execute_dwoutput_one(consume_1_sql)
            result['total_consume_1_num'] = consume_1_result.get('total_consume_1_num', 0) if consume_1_result else 0
            
            # 9. 查询消费2次以上的会员数（最后一天）
            consume_2plus_sql = f"""
            SELECT {MemberBaseSqlQueries.get_dwoutput_total_consume_2plus_num_sql(start_date, end_date, bid, sid)}
            """
            consume_2plus_result = await db.execute_dwoutput_one(consume_2plus_sql)
            result['total_consume_2plus_num'] = consume_2plus_result.get('total_consume_2plus_num', 0) if consume_2plus_result else 0
            
            # 10. 查询新增完善会员量（区间内所有天的求和）
            first_info_sql = MemberBaseSqlQueries.get_dwoutput_total_user_info_num_sql(start_date, end_date, bid, sid)
            first_info_result = await db.execute_dwoutput_one(first_info_sql)
            result['total_first_info_num'] = first_info_result.get('total_user_info_num', 0) if first_info_result else 0
            
            query_time = time.time() - start_time
            logger.info(f"dwoutput数据库查询完成，耗时: {query_time:.3f}秒")
            logger.info(f"dwoutput数据结果: {result}")
            
            return result
            
        except Exception as e:
            logger.error(f"获取dwoutput数据失败: {str(e)}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"dwoutput数据查询失败: {str(e)}")
    
    async def _calculate_new_user_info_rate(self, start_date: str, end_date: str, bid: str, 
                                          sid: Optional[str], dwoutput_data: Dict[str, Any]) -> float:
        """计算新增会员完善率 - 独立查询版本"""
        try:
            logger.debug("开始计算新增会员完善率")
            
            # 从dwoutput获取新增完善会员量
            total_first_info_num = dwoutput_data.get('total_first_info_num', 0) or 0
            total_first_info_num = float(total_first_info_num) if total_first_info_num is not None else 0.0
            
            # 独立查询新增会员数量
            new_user_total_sql = f"SELECT {MemberBaseSqlQueries.get_dwoutput_new_user_total_sql(start_date, end_date, bid, sid)}"
            logger.info(f"新增会员数量查询SQL: {new_user_total_sql}")
            dwoutput_result = await db.execute_dwoutput_one(new_user_total_sql)
            logger.info(f"新增会员数量查询结果: {dwoutput_result}")
            new_user_count = dwoutput_result.get('new_user_total', 0) if dwoutput_result else 0
            new_user_count = float(new_user_count) if new_user_count is not None else 0.0
            
            # 使用重构后的独立计算函数
            rate = MemberBaseSqlCalculator.calculate_new_user_info_rate(total_first_info_num, new_user_count)
            
            logger.info(f"新增会员完善率计算完成: {total_first_info_num}/{new_user_count} = {rate}")
            return rate
            
        except Exception as e:
            logger.error(f"计算新增会员完善率失败: {str(e)}")
            return 0.0

    async def _fetch_member_base_data(self, start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> Dict[str, Any]:
        """获取会员基础数据 - 重构后的版本"""
        try:
            logger.info(f"开始获取会员基础数据 - bid: {bid}, sid: {sid}, 时间范围: {start_date}-{end_date}")
            total_start_time = time.time()
            
            # 并行查询两个数据库
            dwoutput_data_v2, dwoutput_data = await asyncio.gather(
                self._fetch_dwoutput_data_v2(start_date, end_date, bid, sid),
                self._fetch_dwoutput_data(start_date, end_date, bid, sid)
            )
            
            if not dwoutput_data_v2 or not dwoutput_data:
                logger.warning(f"数据库查询结果异常: dwoutput_v2={bool(dwoutput_data_v2)}, dwoutput={bool(dwoutput_data)}")
                return {}

            # 合并数据
            result = {**dwoutput_data_v2, **dwoutput_data}

            # 计算新增会员完善率
            new_user_info_rate = await self._calculate_new_user_info_rate(
                start_date, end_date, bid, sid, dwoutput_data
            )
            result['new_user_info_rate'] = new_user_info_rate

            # 合并会员占比数据
            result = MemberBaseCalculatorAdd.merge_member_ratio_data(result)
            
            total_time = time.time() - total_start_time
            logger.info(f"会员基础数据获取完成，总耗时: {total_time:.3f}秒，数据字段数: {len(result)}")
            
            return result
            
        except Exception as e:
            logger.error(f"获取会员基础数据失败: {str(e)}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"数据查询失败: {str(e)}")
    
    def _calculate_change_rate(self, current: float, previous: float) -> str:
        """计算变化率"""
        if previous == 0:
            return "0%" if current == 0 else "100%"
        
        rate = ((current - previous) / previous) * 100
        return f"{rate:+.2f}%"
    
    def _map_db_result_to_response(self, current_data: Dict[str, Any], 
                                  chain_data: Dict[str, Any], 
                                  year_data: Dict[str, Any]) -> MemberBaseData:
        """将数据库结果映射到响应模型"""
        
        # 映射字段名到数据库字段
        field_mapping = {
            "total_members": ("total_all_user", "人"),
            "net_members": ("net_all_user", "人"),
            "new_members": ("new_user_total", "人"),
            "unfollow_members": ("total_cancel_user", "人"),
            "new_unfollow_members": ("new_cancel_user", "人"),  # 新增字段：新增取关人数
            "unfollow_rate": ("cancel_user_rate", "%"),
            "new_consume_members": ("new_user_consomer_total", "人"),
            "new_charge_members": ("new_user_charger_total", "人"),
            "total_consume_members": ("total_all_user_consomer", "人"),
            "total_charge_members": ("total_all_user_charger", "人"),
            "new_complete_members": ("total_first_info_num", "人"),
            "new_complete_rate": ("new_user_info_rate", "%"),
            "complete_phone_members": ("total_card_phone_num", "人"),
            "total_complete_members": ("total_card_info_num", "人"),
            "phone_member_ratio": ("phone_member_ratio", "%"),
            "prepay_member_ratio": ("prepay_member_ratio", "%"),
            "consume_member_ratio": ("consume_member_ratio", "%"),
            "consume_zero_members": ("total_consume_0_num", "人"),  # 新增字段：消费0次的会员数
            "consume_once_members": ("total_consume_1_num", "人"),
            "consume_multiple_members": ("total_consume_2plus_num", "人")
        }
        
        result = MemberBaseData()
        
        for field_name, (db_field, unit) in field_mapping.items():
            current_value = current_data.get(db_field, 0) or 0
            chain_value = chain_data.get(db_field, 0) or 0
            year_value = year_data.get(db_field, 0) or 0
            
            # 处理百分比字段
            if unit == "%":
                current_value = float(current_value) if current_value else 0.0
                chain_value = float(chain_value) if chain_value else 0.0
                year_value = float(year_value) if year_value else 0.0
                
                # 修复百分比转换 - 问题2和4
                # 确保取关占比和新增会员完善率显示正确的百分比
                if db_field in ['cancel_user_rate', 'new_user_info_rate']:
                    current_value = round(current_value * 100, 2)
                    chain_value = round(chain_value * 100, 2)
                    year_value = round(year_value * 100, 2)
            else:
                current_value = int(current_value) if current_value else 0
                chain_value = int(chain_value) if chain_value else 0
                year_value = int(year_value) if year_value else 0
            
            field_data = FieldDataModel(
                value=current_value,
                unit=unit,
                chain_comparison=[chain_value],
                chain_change_rate=[self._calculate_change_rate(current_value, chain_value)],
                chain_labels=["上期"],
                year_over_year=year_value,
                year_over_year_rate=self._calculate_change_rate(current_value, year_value)
            )
            
            setattr(result, field_name, field_data)
        
        return result
    
    async def get_member_base_data(self, query_params: QueryParams) -> MemberBaseData:
        """获取会员基础数据"""
        try:
            logger.info(f"收到会员基础数据查询请求 - bid: {query_params.bid}, sid: {query_params.sid}, 日期: {query_params.start_date}~{query_params.end_date}")
            
            # 计算时间范围
            time_ranges = self._calculate_time_ranges(query_params)
            logger.debug(f"时间范围计算完成: {time_ranges}")
            
            # 并行获取当前期间、环比期间、同比期间的数据
            current_data, chain_data, year_data = await asyncio.gather(
                self._fetch_member_base_data(
                    time_ranges["current"]["start"],
                    time_ranges["current"]["end"],
                    query_params.bid,
                    query_params.sid
                ),
                self._fetch_member_base_data(
                    time_ranges["chain"]["start"],
                    time_ranges["chain"]["end"],
                    query_params.bid,
                    query_params.sid
                ),
                self._fetch_member_base_data(
                    time_ranges["year_over_year"]["start"],
                    time_ranges["year_over_year"]["end"],
                    query_params.bid,
                    query_params.sid
                )
            )
            
            # 映射数据到响应模型
            result = self._map_db_result_to_response(current_data, chain_data, year_data)
            
            logger.info(f"会员基础数据查询成功完成 - bid: {query_params.bid}, sid: {query_params.sid}")
            return result
            
        except Exception as e:
            logger.error(f"获取会员基础数据失败: {str(e)}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"获取会员基础数据失败: {str(e)}")


# 创建服务实例
member_base_service = MemberBaseService()


@router.post("/member-base", response_model=ResponseModel)
async def get_member_base_data(query_params: QueryParams):
    """获取会员基础数据"""
    try:
        logger.info(f"API接收到会员基础数据请求: {query_params}")
        data = await member_base_service.get_member_base_data(query_params)
        logger.info("会员基础数据API调用成功")
        return ResponseModel(
            code=200,
            message="获取会员基础数据成功",
            data=data
        )
    except HTTPException as e:
        logger.error(f"HTTP异常: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"获取会员基础数据异常: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")
