# -*- coding: utf-8 -*-
"""
阿里云OSS对象存储服务
提供文件上传、下载、删除等基本操作
支持OSS云存储 + 本地留档的双重存储模式
"""

import logging
import hashlib
import datetime
import os
from pathlib import Path
from typing import Dict, Any, Optional, Union

try:
    import oss2
    OSS_AVAILABLE = True
except ImportError:
    OSS_AVAILABLE = False
    logging.warning("oss2库未安装，将使用本地存储模式")

logger = logging.getLogger(__name__)

class OSSService:
    """阿里云OSS对象存储服务类 - 支持OSS云存储 + 本地留档"""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化OSS服务

        Args:
            config: OSS配置参数
        """
        self.config = config or self._get_default_config()
        self.base_url = self.config.get("base_url", "http://localhost:8000")
        self.upload_path = Path(self.config.get("upload_path", "./uploads"))
        self.max_file_size = self.config.get("max_file_size", 50 * 1024 * 1024)  # 50MB

        # OSS配置
        self.oss_enabled = False
        self.bucket = None

        # 确保本地上传目录存在
        self.upload_path.mkdir(parents=True, exist_ok=True)

        # 初始化OSS连接
        self._init_oss_connection()

        logger.info(f"OSS服务初始化完成")
        logger.info(f"  - 本地存储路径: {self.upload_path}")
        logger.info(f"  - OSS云存储: {'启用' if self.oss_enabled else '禁用'}")

    def _init_oss_connection(self):
        """初始化OSS连接"""
        if not OSS_AVAILABLE:
            logger.warning("oss2库未安装，仅使用本地存储")
            return

        try:
            # 从环境变量或配置获取OSS参数
            from core.config import settings

            access_key_id = settings.OSS_ACCESS_KEY_ID
            access_key_secret = settings.OSS_ACCESS_KEY_SECRET
            bucket_name = settings.OSS_BUCKET_NAME
            endpoint = settings.OSS_ENDPOINT

            # 检查配置是否完整
            if not all([access_key_id, access_key_secret, bucket_name, endpoint]) or \
               access_key_id == "your_oss_access_key_id":
                logger.warning("OSS配置不完整，仅使用本地存储")
                return

            # 创建OSS认证和Bucket对象
            auth = oss2.Auth(access_key_id, access_key_secret)
            self.bucket = oss2.Bucket(auth, endpoint, bucket_name)

            # 测试连接
            try:
                self.bucket.get_bucket_info()
                self.oss_enabled = True
                logger.info(f"OSS连接成功 - Bucket: {bucket_name}")
            except Exception as test_error:
                logger.error(f"OSS连接测试失败: {test_error}")
                self.bucket = None

        except Exception as e:
            logger.error(f"初始化OSS连接失败: {e}")
            self.oss_enabled = False

    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "base_url": "http://localhost:8000",
            "upload_path": "./uploads",
            "max_file_size": 50 * 1024 * 1024,
            "allowed_extensions": [".pptx", ".pdf", ".docx", ".xlsx"],
            "url_expire_hours": 24
        }

    def upload_file(
        self,
        file_path: Union[str, Path],
        object_name: Optional[str] = None,
        folder: str = "ppt-reports"
    ) -> Dict[str, Any]:
        """
        上传文件到OSS云存储 + 本地留档

        Args:
            file_path: 本地文件路径
            object_name: 对象名称（可选，默认使用文件名）
            folder: 存储文件夹

        Returns:
            Dict: 上传结果
        """
        try:
            file_path = Path(file_path)

            # 验证文件是否存在
            if not file_path.exists():
                return {
                    "success": False,
                    "error": f"文件不存在: {file_path}",
                    "file_url": None
                }

            # 验证文件大小
            file_size = file_path.stat().st_size
            if file_size > self.max_file_size:
                return {
                    "success": False,
                    "error": f"文件大小超过限制: {file_size / 1024 / 1024:.1f}MB > {self.max_file_size / 1024 / 1024:.1f}MB",
                    "file_url": None
                }

            # 验证文件扩展名
            allowed_extensions = self.config.get("allowed_extensions", [])
            if allowed_extensions and file_path.suffix.lower() not in allowed_extensions:
                return {
                    "success": False,
                    "error": f"不支持的文件类型: {file_path.suffix}",
                    "file_url": None
                }

            # 生成对象名称
            if not object_name:
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                file_hash = self._calculate_file_hash(file_path)[:8]
                object_name = f"{file_path.stem}_{timestamp}_{file_hash}{file_path.suffix}"

            # 1. 保存本地留档副本
            target_folder = self.upload_path / folder
            target_folder.mkdir(parents=True, exist_ok=True)
            target_path = target_folder / object_name

            import shutil
            shutil.copy2(file_path, target_path)
            logger.info(f"本地留档保存成功: {target_path}")

            # 2. 上传到OSS云存储
            oss_success = False
            oss_url = None

            if self.oss_enabled and self.bucket:
                try:
                    # OSS对象路径
                    oss_object_key = f"{folder}/{object_name}"

                    # 上传到OSS
                    result = self.bucket.put_object_from_file(oss_object_key, str(file_path))

                    # 生成OSS访问URL
                    oss_url = self._generate_oss_url(oss_object_key)
                    oss_success = True

                    logger.info(f"OSS上传成功: {oss_object_key}")
                    logger.info(f"OSS ETag: {result.etag}")

                except Exception as oss_error:
                    logger.error(f"OSS上传失败: {oss_error}")
                    # OSS失败不影响整体流程，继续使用本地存储

            # 3. 确定最终的访问URL
            if oss_success and oss_url:
                file_url = oss_url
                storage_type = "OSS云存储"
            else:
                file_url = self._generate_file_url(folder, object_name)
                storage_type = "本地存储"

            logger.info(f"文件上传完成:")
            logger.info(f"  - 源文件: {file_path}")
            logger.info(f"  - 本地留档: {target_path}")
            logger.info(f"  - 主要存储: {storage_type}")
            logger.info(f"  - 访问URL: {file_url}")

            return {
                "success": True,
                "message": f"文件上传成功 ({storage_type})",
                "file_url": file_url,
                "file_path": str(target_path),
                "object_name": object_name,
                "folder": folder,
                "file_size": file_size,
                "upload_time": datetime.datetime.now().isoformat(),
                "storage_type": storage_type,
                "oss_enabled": oss_success
            }

        except Exception as e:
            error_msg = f"文件上传失败: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "success": False,
                "error": error_msg,
                "file_url": None
            }

    def download_file(self, object_name: str, folder: str = "ppt-reports") -> Dict[str, Any]:
        """
        获取文件下载信息 - 优先使用OSS，本地作为备选

        Args:
            object_name: 对象名称
            folder: 文件夹

        Returns:
            Dict: 下载信息
        """
        try:
            # 安全检查：防止路径遍历攻击
            if ".." in object_name or ".." in folder:
                return {
                    "success": False,
                    "error": f"无效的文件路径: {folder}/{object_name}",
                    "file_path": None
                }

            # 1. 优先尝试OSS下载
            if self.oss_enabled and self.bucket:
                try:
                    oss_object_key = f"{folder}/{object_name}"

                    # 检查OSS中文件是否存在
                    if self.bucket.object_exists(oss_object_key):
                        # 生成预签名下载URL（1小时有效期）
                        presigned_url = self.bucket.sign_url('GET', oss_object_key, 3600)

                        # 获取文件信息
                        object_info = self.bucket.head_object(oss_object_key)
                        file_size = object_info.content_length

                        logger.info(f"OSS文件下载信息获取成功: {oss_object_key}")

                        return {
                            "success": True,
                            "file_url": presigned_url,
                            "file_path": None,  # OSS模式下不返回本地路径
                            "file_size": file_size,
                            "object_name": object_name,
                            "folder": folder,
                            "storage_type": "OSS云存储",
                            "download_type": "presigned_url"
                        }

                except Exception as oss_error:
                    logger.warning(f"OSS下载失败，尝试本地存储: {oss_error}")

            # 2. 备选方案：本地文件下载
            file_path = self.upload_path / folder / object_name

            # 确保文件路径在允许的目录内
            try:
                file_path = file_path.resolve()
                upload_path_resolved = self.upload_path.resolve()
                if not str(file_path).startswith(str(upload_path_resolved)):
                    return {
                        "success": False,
                        "error": f"文件路径超出允许范围: {object_name}",
                        "file_path": None
                    }
            except Exception as path_error:
                logger.error(f"路径解析失败: {path_error}")
                return {
                    "success": False,
                    "error": f"路径解析失败: {object_name}",
                    "file_path": None
                }

            if not file_path.exists():
                logger.warning(f"本地文件不存在: {file_path}")
                return {
                    "success": False,
                    "error": f"文件不存在: {object_name}",
                    "file_path": None
                }

            # 检查文件大小
            file_size = file_path.stat().st_size
            if file_size == 0:
                logger.warning(f"文件大小为0: {file_path}")
                return {
                    "success": False,
                    "error": f"文件为空: {object_name}",
                    "file_path": None
                }

            file_url = self._generate_file_url(folder, object_name)

            logger.info(f"本地文件下载信息获取成功: {file_path} (大小: {file_size} bytes)")

            return {
                "success": True,
                "file_url": file_url,
                "file_path": str(file_path),
                "file_size": file_size,
                "object_name": object_name,
                "folder": folder,
                "storage_type": "本地存储",
                "download_type": "local_file"
            }

        except Exception as e:
            error_msg = f"获取文件下载信息失败: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "success": False,
                "error": error_msg,
                "file_path": None
            }

    def delete_file(self, object_name: str, folder: str = "ppt-reports") -> Dict[str, Any]:
        """
        删除文件 - 同时删除OSS和本地文件

        Args:
            object_name: 对象名称
            folder: 文件夹

        Returns:
            Dict: 删除结果
        """
        try:
            oss_deleted = False
            local_deleted = False

            # 1. 删除OSS中的文件
            if self.oss_enabled and self.bucket:
                try:
                    oss_object_key = f"{folder}/{object_name}"
                    self.bucket.delete_object(oss_object_key)
                    oss_deleted = True
                    logger.info(f"OSS文件删除成功: {oss_object_key}")
                except Exception as oss_error:
                    logger.warning(f"OSS文件删除失败: {oss_error}")

            # 2. 删除本地文件
            file_path = self.upload_path / folder / object_name
            if file_path.exists():
                file_path.unlink()
                local_deleted = True
                logger.info(f"本地文件删除成功: {file_path}")
            else:
                logger.warning(f"本地文件不存在: {file_path}")

            if not oss_deleted and not local_deleted:
                return {
                    "success": False,
                    "error": f"文件不存在: {object_name}"
                }

            return {
                "success": True,
                "message": f"文件删除成功 (OSS: {'是' if oss_deleted else '否'}, 本地: {'是' if local_deleted else '否'})",
                "object_name": object_name,
                "folder": folder,
                "oss_deleted": oss_deleted,
                "local_deleted": local_deleted
            }

        except Exception as e:
            error_msg = f"文件删除失败: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg
            }

    def list_files(self, folder: str = "ppt-reports") -> Dict[str, Any]:
        """
        列出文件夹中的文件

        Args:
            folder: 文件夹

        Returns:
            Dict: 文件列表
        """
        try:
            folder_path = self.upload_path / folder

            if not folder_path.exists():
                return {
                    "success": True,
                    "files": [],
                    "total_count": 0
                }

            files = []
            for file_path in folder_path.iterdir():
                if file_path.is_file():
                    file_info = {
                        "object_name": file_path.name,
                        "file_size": file_path.stat().st_size,
                        "modified_time": datetime.datetime.fromtimestamp(file_path.stat().st_mtime).isoformat(),
                        "file_url": self._generate_file_url(folder, file_path.name)
                    }
                    files.append(file_info)

            return {
                "success": True,
                "files": files,
                "total_count": len(files),
                "folder": folder
            }

        except Exception as e:
            error_msg = f"列出文件失败: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "files": []
            }

    def _calculate_file_hash(self, file_path: Path) -> str:
        """计算文件哈希值"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()

    def _generate_file_url(self, folder: str, object_name: str) -> str:
        """生成本地文件访问URL"""
        # 修复URL路径，确保与router.py中的路由定义一致
        return f"{self.base_url}/api/ppt-report/download/{folder}/{object_name}"

    def _generate_oss_url(self, object_key: str) -> str:
        """生成OSS文件访问URL（预签名URL，更安全）"""
        if not self.bucket:
            return None

        try:
            # 生成1小时有效期的预签名URL（更安全的方式）
            return self.bucket.sign_url('GET', object_key, 3600)

        except Exception as e:
            logger.error(f"生成OSS预签名URL失败: {e}")
            return None

    def generate_presigned_download_url(self, object_name: str, folder: str = "ppt-reports", expires: int = 3600) -> Optional[str]:
        """
        生成OSS预签名下载URL

        Args:
            object_name: 对象名称
            folder: 文件夹
            expires: 过期时间（秒）

        Returns:
            预签名URL或None
        """
        if not self.oss_enabled or not self.bucket:
            return None

        try:
            oss_object_key = f"{folder}/{object_name}"
            return self.bucket.sign_url('GET', oss_object_key, expires)
        except Exception as e:
            logger.error(f"生成预签名URL失败: {e}")
            return None

    def get_file_info(self, object_name: str, folder: str = "ppt-reports") -> Dict[str, Any]:
        """
        获取文件信息

        Args:
            object_name: 对象名称
            folder: 文件夹

        Returns:
            Dict: 文件信息
        """
        try:
            file_path = self.upload_path / folder / object_name

            if not file_path.exists():
                return {
                    "success": False,
                    "error": f"文件不存在: {object_name}"
                }

            stat = file_path.stat()

            return {
                "success": True,
                "object_name": object_name,
                "folder": folder,
                "file_size": stat.st_size,
                "file_size_mb": round(stat.st_size / 1024 / 1024, 2),
                "created_time": datetime.datetime.fromtimestamp(stat.st_ctime).isoformat(),
                "modified_time": datetime.datetime.fromtimestamp(stat.st_mtime).isoformat(),
                "file_url": self._generate_file_url(folder, object_name),
                "file_extension": file_path.suffix.lower()
            }

        except Exception as e:
            error_msg = f"获取文件信息失败: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg
            }


# 创建全局OSS服务实例
oss_service = OSSService()